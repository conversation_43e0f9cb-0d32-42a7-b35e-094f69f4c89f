"""
英文标签图表生成 - 彻底解决字体显示问题
使用英文标签确保在任何环境下都能正常显示
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from gwpca import GWPCA
import warnings
import os

# 创建输出目录
OUTPUT_DIR = "output/demo"
os.makedirs(f"{OUTPUT_DIR}/figures_english", exist_ok=True)

# 设置字体为系统默认，确保稳定显示
plt.rcParams['font.family'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.ioff()  # 关闭交互模式
sns.set_style("whitegrid")
warnings.filterwarnings('ignore')

def generate_test_data():
    """生成测试数据"""
    np.random.seed(42)
    
    # 生成随机分布的坐标点
    coordinates = np.random.uniform(0, 10, (150, 2))
    
    # 使用英文特征名称
    feature_names = [
        'Population Density',
        'Economic Development', 
        'Education Level',
        'Environmental Quality',
        'Transportation Access'
    ]
    
    n_features = len(feature_names)
    data = np.zeros((len(coordinates), n_features))
    
    for i, (x, y) in enumerate(coordinates):
        # 人口密度：城市中心（5,5）附近密度高
        center_dist = np.sqrt((x - 5)**2 + (y - 5)**2)
        data[i, 0] = 100 * np.exp(-center_dist/3) + np.random.normal(0, 10)
        
        # 经济发展水平：与人口密度正相关，但有东西梯度
        data[i, 1] = 0.8 * data[i, 0] + 5 * x + np.random.normal(0, 15)
        
        # 教育水平：与经济发展相关，但有南北差异
        data[i, 2] = 0.6 * data[i, 1] + 3 * y + np.random.normal(0, 12)
        
        # 环境质量：远离中心区域质量更好
        data[i, 3] = 80 - 0.3 * data[i, 0] + np.random.normal(0, 8)
        
        # 交通便利性：主要道路（对角线）附近更便利
        road_dist = abs(x - y) + abs(x + y - 10)
        data[i, 4] = 60 - 2 * road_dist + 0.2 * data[i, 0] + np.random.normal(0, 10)
    
    # 确保数据为正值
    data = np.maximum(data, 0)
    
    return coordinates, data, feature_names

def create_professional_charts():
    """创建专业的英文图表"""
    print("🎨 Creating professional English charts...")
    
    # 生成数据
    coordinates, data, feature_names = generate_test_data()
    
    # 设置专业的颜色方案
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']
    
    # 1. 原始数据空间分布
    print("📊 Generating spatial distribution of original data...")
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.ravel()
    
    for i in range(len(feature_names)):
        scatter = axes[i].scatter(coordinates[:, 0], coordinates[:, 1], 
                                c=data[:, i], cmap='viridis', s=60, alpha=0.8, edgecolors='white', linewidth=0.5)
        axes[i].set_title(feature_names[i], fontsize=14, fontweight='bold', pad=20)
        axes[i].set_xlabel('X Coordinate', fontsize=12)
        axes[i].set_ylabel('Y Coordinate', fontsize=12)
        
        # 添加颜色条
        cbar = plt.colorbar(scatter, ax=axes[i])
        cbar.ax.tick_params(labelsize=10)
        
        # 美化网格
        axes[i].grid(True, alpha=0.3, linestyle='--')
        axes[i].set_xlim(-0.5, 10.5)
        axes[i].set_ylim(-0.5, 10.5)
    
    # 隐藏多余的子图
    if len(feature_names) < 6:
        axes[5].set_visible(False)
    
    plt.suptitle('Spatial Distribution of Original Data\n(Geographically Weighted PCA Analysis)', 
                 fontsize=18, fontweight='bold', y=0.98)
    plt.tight_layout(rect=[0, 0.03, 1, 0.95])
    plt.savefig(f"{OUTPUT_DIR}/figures_english/01_spatial_data_distribution.png", 
                dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    # 执行GWPCA
    print("🔍 Performing GWPCA analysis...")
    gwpca = GWPCA(n_components=3, bandwidth='adaptive', kernel='gaussian')
    X_transformed = gwpca.fit_transform(data, coordinates)
    
    print(f"   - Selected bandwidth: {gwpca.bandwidth_:.3f}")
    print(f"   - Transformed data shape: {X_transformed.shape}")
    
    # 2. 主成分得分空间分布
    print("📈 Generating principal component scores distribution...")
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    pc_labels = ['PC1 Scores', 'PC2 Scores', 'PC3 Scores']
    cmaps = ['RdBu_r', 'RdYlBu_r', 'RdYlGn_r']
    
    for i in range(3):
        scatter = axes[i].scatter(coordinates[:, 0], coordinates[:, 1], 
                                c=X_transformed[:, i], cmap=cmaps[i], s=60, alpha=0.8, 
                                edgecolors='white', linewidth=0.5)
        axes[i].set_title(pc_labels[i], fontsize=14, fontweight='bold', pad=20)
        axes[i].set_xlabel('X Coordinate', fontsize=12)
        axes[i].set_ylabel('Y Coordinate', fontsize=12)
        
        # 添加颜色条
        cbar = plt.colorbar(scatter, ax=axes[i])
        cbar.ax.tick_params(labelsize=10)
        
        # 美化网格
        axes[i].grid(True, alpha=0.3, linestyle='--')
        axes[i].set_xlim(-0.5, 10.5)
        axes[i].set_ylim(-0.5, 10.5)
    
    plt.suptitle('Spatial Distribution of Principal Component Scores\n(Local PCA Results)', 
                 fontsize=18, fontweight='bold', y=1.02)
    plt.tight_layout(rect=[0, 0.03, 1, 0.95])
    plt.savefig(f"{OUTPUT_DIR}/figures_english/02_pc_scores_spatial.png", 
                dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    # 3. 局部解释方差比例空间分布
    print("📊 Generating explained variance ratio distribution...")
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    var_labels = ['PC1 Explained Variance Ratio', 'PC2 Explained Variance Ratio', 'PC3 Explained Variance Ratio']
    
    for i in range(3):
        scatter = axes[i].scatter(coordinates[:, 0], coordinates[:, 1], 
                                c=gwpca.local_explained_variance_ratio_[:, i], 
                                cmap='plasma', s=60, alpha=0.8, edgecolors='white', linewidth=0.5)
        axes[i].set_title(var_labels[i], fontsize=14, fontweight='bold', pad=20)
        axes[i].set_xlabel('X Coordinate', fontsize=12)
        axes[i].set_ylabel('Y Coordinate', fontsize=12)
        
        # 添加颜色条
        cbar = plt.colorbar(scatter, ax=axes[i])
        cbar.ax.tick_params(labelsize=10)
        cbar.set_label('Ratio', fontsize=10)
        
        # 美化网格
        axes[i].grid(True, alpha=0.3, linestyle='--')
        axes[i].set_xlim(-0.5, 10.5)
        axes[i].set_ylim(-0.5, 10.5)
    
    plt.suptitle('Spatial Distribution of Local Explained Variance Ratios\n(Spatial Heterogeneity in PCA)', 
                 fontsize=18, fontweight='bold', y=1.02)
    plt.tight_layout(rect=[0, 0.03, 1, 0.95])
    plt.savefig(f"{OUTPUT_DIR}/figures_english/03_explained_variance_spatial.png", 
                dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    # 4. 第一主成分载荷空间分布
    print("📈 Generating PC1 loadings distribution...")
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.ravel()
    
    loading_labels = [f'{name}\nLoading' for name in feature_names]
    
    for i in range(len(feature_names)):
        loadings = gwpca.local_components_[:, 0, i]
        scatter = axes[i].scatter(coordinates[:, 0], coordinates[:, 1], 
                                c=loadings, cmap='RdBu_r', s=60, alpha=0.8, 
                                edgecolors='white', linewidth=0.5)
        axes[i].set_title(loading_labels[i], fontsize=14, fontweight='bold', pad=20)
        axes[i].set_xlabel('X Coordinate', fontsize=12)
        axes[i].set_ylabel('Y Coordinate', fontsize=12)
        
        # 添加颜色条
        cbar = plt.colorbar(scatter, ax=axes[i])
        cbar.ax.tick_params(labelsize=10)
        cbar.set_label('Loading', fontsize=10)
        
        # 美化网格
        axes[i].grid(True, alpha=0.3, linestyle='--')
        axes[i].set_xlim(-0.5, 10.5)
        axes[i].set_ylim(-0.5, 10.5)
    
    # 隐藏多余的子图
    if len(feature_names) < 6:
        axes[5].set_visible(False)
    
    plt.suptitle('Spatial Distribution of First Principal Component Loadings\n(Local Feature Contributions)', 
                 fontsize=18, fontweight='bold', y=0.98)
    plt.tight_layout(rect=[0, 0.03, 1, 0.95])
    plt.savefig(f"{OUTPUT_DIR}/figures_english/04_pc1_loadings_spatial.png", 
                dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    # 5. 创建综合分析图
    print("📊 Generating comprehensive analysis summary...")
    fig = plt.figure(figsize=(20, 16))
    
    # 创建网格布局
    gs = fig.add_gridspec(4, 4, hspace=0.3, wspace=0.3)
    
    # 原始数据（选择3个主要特征）
    main_features = [0, 1, 3]  # 人口密度、经济发展、环境质量
    for idx, i in enumerate(main_features):
        ax = fig.add_subplot(gs[0, idx])
        scatter = ax.scatter(coordinates[:, 0], coordinates[:, 1], 
                           c=data[:, i], cmap='viridis', s=30, alpha=0.8)
        ax.set_title(feature_names[i], fontsize=12, fontweight='bold')
        ax.set_xlabel('X', fontsize=10)
        ax.set_ylabel('Y', fontsize=10)
        plt.colorbar(scatter, ax=ax)
        ax.grid(True, alpha=0.3)
    
    # 主成分得分
    for i in range(3):
        ax = fig.add_subplot(gs[1, i])
        scatter = ax.scatter(coordinates[:, 0], coordinates[:, 1], 
                           c=X_transformed[:, i], cmap='RdBu_r', s=30, alpha=0.8)
        ax.set_title(f'PC{i+1} Scores', fontsize=12, fontweight='bold')
        ax.set_xlabel('X', fontsize=10)
        ax.set_ylabel('Y', fontsize=10)
        plt.colorbar(scatter, ax=ax)
        ax.grid(True, alpha=0.3)
    
    # 解释方差比例
    for i in range(3):
        ax = fig.add_subplot(gs[2, i])
        scatter = ax.scatter(coordinates[:, 0], coordinates[:, 1], 
                           c=gwpca.local_explained_variance_ratio_[:, i], 
                           cmap='plasma', s=30, alpha=0.8)
        ax.set_title(f'PC{i+1} Explained Var.', fontsize=12, fontweight='bold')
        ax.set_xlabel('X', fontsize=10)
        ax.set_ylabel('Y', fontsize=10)
        plt.colorbar(scatter, ax=ax)
        ax.grid(True, alpha=0.3)
    
    # 统计信息
    ax_stats = fig.add_subplot(gs[3, :])
    ax_stats.axis('off')
    
    # 添加统计文本
    summary = gwpca.get_local_summary()
    stats_text = f"""
GWPCA Analysis Summary:
• Sample Size: {len(coordinates)} locations
• Features: {len(feature_names)} variables
• Selected Bandwidth: {gwpca.bandwidth_:.3f}
• PC1 Avg. Explained Variance: {summary.iloc[0]['平均解释方差比例']:.3f}
• PC2 Avg. Explained Variance: {summary.iloc[1]['平均解释方差比例']:.3f}
• PC3 Avg. Explained Variance: {summary.iloc[2]['平均解释方差比例']:.3f}
• Total Explained Variance: {summary['累积解释方差比例'].iloc[2]:.3f}
    """
    
    ax_stats.text(0.1, 0.5, stats_text, fontsize=14, verticalalignment='center',
                 bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))
    
    plt.suptitle('Geographically Weighted Principal Component Analysis (GWPCA)\nComprehensive Results Overview', 
                 fontsize=20, fontweight='bold', y=0.98)
    
    plt.savefig(f"{OUTPUT_DIR}/figures_english/05_comprehensive_analysis.png", 
                dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    print("✅ All professional English charts generated successfully!")
    print(f"📁 Saved to: {OUTPUT_DIR}/figures_english/")
    print("📋 Generated files:")
    print("   - 01_spatial_data_distribution.png")
    print("   - 02_pc_scores_spatial.png") 
    print("   - 03_explained_variance_spatial.png")
    print("   - 04_pc1_loadings_spatial.png")
    print("   - 05_comprehensive_analysis.png")
    
    return gwpca, coordinates, data, feature_names

def save_english_data(gwpca, coordinates, data, feature_names):
    """保存英文版数据文件"""
    print("💾 Saving English data files...")
    
    # 原始数据
    coords_df = pd.DataFrame(coordinates, columns=['X_Coordinate', 'Y_Coordinate'])
    data_df = pd.DataFrame(data, columns=feature_names)
    original_df = pd.concat([coords_df, data_df], axis=1)
    original_df.to_csv(f"{OUTPUT_DIR}/data/original_data_english.csv", index=False)
    
    # 主成分得分
    pc_scores = gwpca.transform(data)
    pc_scores_df = pd.DataFrame(pc_scores, columns=['PC1_Score', 'PC2_Score', 'PC3_Score'])
    scores_with_coords = pd.concat([coords_df, pc_scores_df], axis=1)
    scores_with_coords.to_csv(f"{OUTPUT_DIR}/data/pc_scores_english.csv", index=False)
    
    # 局部解释方差
    var_df = pd.DataFrame(gwpca.local_explained_variance_ratio_, 
                         columns=['PC1_Explained_Var', 'PC2_Explained_Var', 'PC3_Explained_Var'])
    var_with_coords = pd.concat([coords_df, var_df], axis=1)
    var_with_coords.to_csv(f"{OUTPUT_DIR}/data/local_explained_variance_english.csv", index=False)
    
    print("✅ English data files saved successfully!")

def main():
    """主函数"""
    print("🌍 Professional GWPCA Analysis with English Labels")
    print("=" * 60)
    print("🎯 Objective: Generate publication-ready charts with clear English labels")
    print("📊 Features: High-resolution, professional styling, comprehensive analysis")
    print()
    
    # 创建图表
    gwpca, coords, data, names = create_professional_charts()
    
    # 保存数据
    save_english_data(gwpca, coords, data, names)
    
    print("\n" + "=" * 60)
    print("🎉 Professional GWPCA Analysis Complete!")
    print("📈 All charts use clear English labels - no font issues!")
    print("🔬 Ready for academic publication and professional presentation")
    print(f"📁 Location: {OUTPUT_DIR}/figures_english/")
    print("\n💡 Usage Tips:")
    print("- All images are 300 DPI for high-quality printing")
    print("- Professional color schemes for better visualization")
    print("- Comprehensive analysis overview included")
    print("- Data files available in English for further analysis")

if __name__ == "__main__":
    main()
