"""
修复图片中文显示问题的演示文件
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import seaborn as sns
from gwpca import GWPCA
import warnings
import os

# 创建输出目录
OUTPUT_DIR = "output/demo"
os.makedirs(f"{OUTPUT_DIR}/figures_fixed", exist_ok=True)

# 设置中文字体
def setup_chinese_font():
    """设置中文字体"""
    # 尝试多种中文字体设置方案
    chinese_fonts = [
        'Microsoft YaHei',  # 微软雅黑
        'SimHei',          # 黑体
        'KaiTi',           # 楷体
        'SimSun',          # 宋体
        'FangSong',        # 仿宋
        'STSong',          # 华文宋体
        'STKaiti',         # 华文楷体
        'STHeiti',         # 华文黑体
        'Arial Unicode MS', # Arial Unicode MS
        'DejaVu Sans'      # 备用字体
    ]
    
    # 检查可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    print("可用字体示例:", available_fonts[:10])
    
    for font in chinese_fonts:
        if font in available_fonts:
            plt.rcParams['font.sans-serif'] = [font]
            plt.rcParams['axes.unicode_minus'] = False
            print(f"✅ 使用字体: {font}")
            return True
    
    # 如果没有找到中文字体，使用英文
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    print("⚠️ 未找到中文字体，将使用英文标签")
    return False

# 设置字体
use_chinese = setup_chinese_font()
plt.ioff()  # 关闭交互模式
sns.set_style("whitegrid")
warnings.filterwarnings('ignore')


def generate_test_data():
    """生成测试数据"""
    np.random.seed(42)
    
    # 生成随机分布的坐标点
    coordinates = np.random.uniform(0, 10, (100, 2))
    
    # 特征名称
    if use_chinese:
        feature_names = ['人口密度', '经济发展水平', '教育水平', '环境质量', '交通便利性']
    else:
        feature_names = ['Population Density', 'Economic Development', 'Education Level', 
                        'Environmental Quality', 'Transportation Accessibility']
    
    n_features = len(feature_names)
    data = np.zeros((len(coordinates), n_features))
    
    for i, (x, y) in enumerate(coordinates):
        # 人口密度：城市中心（5,5）附近密度高
        center_dist = np.sqrt((x - 5)**2 + (y - 5)**2)
        data[i, 0] = 100 * np.exp(-center_dist/3) + np.random.normal(0, 10)
        
        # 经济发展水平：与人口密度正相关，但有东西梯度
        data[i, 1] = 0.8 * data[i, 0] + 5 * x + np.random.normal(0, 15)
        
        # 教育水平：与经济发展相关，但有南北差异
        data[i, 2] = 0.6 * data[i, 1] + 3 * y + np.random.normal(0, 12)
        
        # 环境质量：远离中心区域质量更好
        data[i, 3] = 80 - 0.3 * data[i, 0] + np.random.normal(0, 8)
        
        # 交通便利性：主要道路（对角线）附近更便利
        road_dist = abs(x - y) + abs(x + y - 10)
        data[i, 4] = 60 - 2 * road_dist + 0.2 * data[i, 0] + np.random.normal(0, 10)
    
    # 确保数据为正值
    data = np.maximum(data, 0)
    
    return coordinates, data, feature_names


def create_fixed_charts():
    """创建修复后的图表"""
    print("🔧 开始生成修复后的图表...")
    
    # 生成数据
    coordinates, data, feature_names = generate_test_data()
    
    # 设置标签
    if use_chinese:
        x_label, y_label = 'X坐标', 'Y坐标'
        titles = {
            'spatial_dist': '原始数据空间分布',
            'pc_scores': '主成分得分的空间分布',
            'explained_var': '局部解释方差比例的空间分布',
            'pc1_loadings': '第一主成分载荷的空间分布'
        }
        pc_labels = [f'第{i+1}主成分得分' for i in range(3)]
        var_labels = [f'第{i+1}主成分解释方差比例' for i in range(3)]
        loading_labels = [f'{name}的载荷' for name in feature_names]
    else:
        x_label, y_label = 'X Coordinate', 'Y Coordinate'
        titles = {
            'spatial_dist': 'Spatial Distribution of Original Data',
            'pc_scores': 'Spatial Distribution of PC Scores',
            'explained_var': 'Spatial Distribution of Local Explained Variance Ratio',
            'pc1_loadings': 'Spatial Distribution of PC1 Loadings'
        }
        pc_labels = [f'PC{i+1} Scores' for i in range(3)]
        var_labels = [f'PC{i+1} Explained Variance Ratio' for i in range(3)]
        loading_labels = [f'{name} Loading' for name in feature_names]
    
    # 1. 原始数据空间分布
    print("📊 生成原始数据空间分布图...")
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    axes = axes.ravel()
    
    for i in range(len(feature_names)):
        scatter = axes[i].scatter(coordinates[:, 0], coordinates[:, 1], 
                                c=data[:, i], cmap='viridis', s=50, alpha=0.7)
        axes[i].set_title(feature_names[i], fontsize=12)
        axes[i].set_xlabel(x_label)
        axes[i].set_ylabel(y_label)
        plt.colorbar(scatter, ax=axes[i])
    
    if len(feature_names) < 6:
        axes[5].set_visible(False)
    
    plt.suptitle(titles['spatial_dist'], fontsize=16)
    plt.tight_layout()
    plt.savefig(f"{OUTPUT_DIR}/figures_fixed/spatial_data_distribution_fixed.png", 
                dpi=300, bbox_inches='tight')
    plt.close()
    
    # 执行GWPCA
    print("🔍 执行GWPCA分析...")
    gwpca = GWPCA(n_components=3, bandwidth='adaptive', kernel='gaussian')
    X_transformed = gwpca.fit_transform(data, coordinates)
    
    # 2. 主成分得分空间分布
    print("📈 生成主成分得分空间分布图...")
    fig, axes = plt.subplots(1, 3, figsize=(15, 4))
    
    for i in range(3):
        scatter = axes[i].scatter(coordinates[:, 0], coordinates[:, 1], 
                                c=X_transformed[:, i], cmap='RdBu_r', s=50, alpha=0.7)
        axes[i].set_title(pc_labels[i], fontsize=12)
        axes[i].set_xlabel(x_label)
        axes[i].set_ylabel(y_label)
        plt.colorbar(scatter, ax=axes[i])
    
    plt.suptitle(titles['pc_scores'], fontsize=16)
    plt.tight_layout()
    plt.savefig(f"{OUTPUT_DIR}/figures_fixed/pc_scores_spatial_fixed.png", 
                dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. 局部解释方差比例空间分布
    print("📊 生成解释方差比例空间分布图...")
    fig, axes = plt.subplots(1, 3, figsize=(15, 4))
    
    for i in range(3):
        scatter = axes[i].scatter(coordinates[:, 0], coordinates[:, 1], 
                                c=gwpca.local_explained_variance_ratio_[:, i], 
                                cmap='plasma', s=50, alpha=0.7)
        axes[i].set_title(var_labels[i], fontsize=12)
        axes[i].set_xlabel(x_label)
        axes[i].set_ylabel(y_label)
        plt.colorbar(scatter, ax=axes[i])
    
    plt.suptitle(titles['explained_var'], fontsize=16)
    plt.tight_layout()
    plt.savefig(f"{OUTPUT_DIR}/figures_fixed/explained_variance_spatial_fixed.png", 
                dpi=300, bbox_inches='tight')
    plt.close()
    
    # 4. 第一主成分载荷空间分布
    print("📈 生成第一主成分载荷空间分布图...")
    fig, axes = plt.subplots(2, 3, figsize=(15, 8))
    axes = axes.ravel()
    
    for i in range(len(feature_names)):
        loadings = gwpca.local_components_[:, 0, i]
        scatter = axes[i].scatter(coordinates[:, 0], coordinates[:, 1], 
                                c=loadings, cmap='RdBu_r', s=50, alpha=0.7)
        axes[i].set_title(loading_labels[i], fontsize=12)
        axes[i].set_xlabel(x_label)
        axes[i].set_ylabel(y_label)
        plt.colorbar(scatter, ax=axes[i])
    
    if len(feature_names) < 6:
        axes[5].set_visible(False)
    
    plt.suptitle(titles['pc1_loadings'], fontsize=16)
    plt.tight_layout()
    plt.savefig(f"{OUTPUT_DIR}/figures_fixed/pc1_loadings_spatial_fixed.png", 
                dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 所有修复后的图表已生成完成！")
    print(f"📁 保存位置: {OUTPUT_DIR}/figures_fixed/")
    print("📋 生成的文件:")
    print("   - spatial_data_distribution_fixed.png")
    print("   - pc_scores_spatial_fixed.png")
    print("   - explained_variance_spatial_fixed.png")
    print("   - pc1_loadings_spatial_fixed.png")
    
    return gwpca, coordinates, data, feature_names


if __name__ == "__main__":
    print("🔧 修复图表中文显示问题")
    print("=" * 50)
    
    # 测试字体
    print("\n🔍 测试字体设置...")
    test_fig, test_ax = plt.subplots(figsize=(6, 4))
    if use_chinese:
        test_ax.set_title("中文字体测试 - 人口密度分布")
        test_ax.set_xlabel("X坐标")
        test_ax.set_ylabel("Y坐标")
    else:
        test_ax.set_title("Font Test - Population Density Distribution")
        test_ax.set_xlabel("X Coordinate")
        test_ax.set_ylabel("Y Coordinate")
    
    # 简单测试图
    x = np.random.randn(50)
    y = np.random.randn(50)
    test_ax.scatter(x, y, alpha=0.7)
    plt.tight_layout()
    plt.savefig(f"{OUTPUT_DIR}/figures_fixed/font_test.png", dpi=300, bbox_inches='tight')
    plt.close()
    print(f"✅ 字体测试图已保存: {OUTPUT_DIR}/figures_fixed/font_test.png")
    
    # 生成修复后的图表
    gwpca, coords, data, names = create_fixed_charts()
    
    print("\n🎯 图表修复完成！")
    if use_chinese:
        print("💡 提示：现在图表中的中文应该能正确显示了")
    else:
        print("💡 Note: Charts are generated with English labels due to font limitations")
