"""
终极字体修复方案 - 彻底解决中文显示问题
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import seaborn as sns
from gwpca import GWPCA
import warnings
import os
import urllib.request
from pathlib import Path

# 创建输出目录
OUTPUT_DIR = "output/demo"
FONT_DIR = f"{OUTPUT_DIR}/fonts"
os.makedirs(f"{OUTPUT_DIR}/figures_ultimate", exist_ok=True)
os.makedirs(FONT_DIR, exist_ok=True)

warnings.filterwarnings('ignore')

def download_chinese_font():
    """下载中文字体文件"""
    font_url = "https://github.com/adobe-fonts/source-han-sans/raw/release/OTF/SimplifiedChinese/SourceHanSansSC-Regular.otf"
    font_path = os.path.join(FONT_DIR, "SourceHanSansSC-Regular.otf")
    
    if not os.path.exists(font_path):
        try:
            print("📥 正在下载中文字体...")
            urllib.request.urlretrieve(font_url, font_path)
            print(f"✅ 字体下载成功: {font_path}")
            return font_path
        except Exception as e:
            print(f"❌ 字体下载失败: {e}")
            return None
    else:
        print(f"✅ 字体文件已存在: {font_path}")
        return font_path

def setup_ultimate_chinese_font():
    """终极中文字体设置方案"""
    print("🔧 开始终极字体修复...")
    
    # 方案1: 尝试下载字体
    font_path = download_chinese_font()
    if font_path and os.path.exists(font_path):
        try:
            # 注册字体
            fm.fontManager.addfont(font_path)
            plt.rcParams['font.family'] = ['Source Han Sans SC']
            plt.rcParams['axes.unicode_minus'] = False
            print("✅ 使用下载的Source Han Sans SC字体")
            return True, "Source Han Sans SC"
        except Exception as e:
            print(f"❌ 下载字体设置失败: {e}")
    
    # 方案2: 尝试系统字体（更全面的列表）
    chinese_fonts = [
        'Microsoft YaHei',
        'Microsoft YaHei UI', 
        'SimHei',
        'SimSun',
        'KaiTi',
        'FangSong',
        'STSong',
        'STKaiti', 
        'STHeiti',
        'STFangsong',
        'STXihei',
        'STLiti',
        'STXingkai',
        'STXinwei',
        'STHupo',
        'STCaiyun',
        'STYaoti',
        'LiSu',
        'YouYuan',
        'PMingLiU',
        'MingLiU',
        'DFKai-SB',
        'Arial Unicode MS',
        'Lucida Sans Unicode'
    ]
    
    # 获取所有可用字体
    available_fonts = set(f.name for f in fm.fontManager.ttflist)
    print(f"🔍 系统中发现 {len(available_fonts)} 个字体")
    
    # 查找中文字体
    found_chinese_fonts = []
    for font in chinese_fonts:
        if font in available_fonts:
            found_chinese_fonts.append(font)
    
    print(f"📋 找到的中文字体: {found_chinese_fonts}")
    
    if found_chinese_fonts:
        selected_font = found_chinese_fonts[0]
        plt.rcParams['font.family'] = [selected_font]
        plt.rcParams['axes.unicode_minus'] = False
        print(f"✅ 使用系统字体: {selected_font}")
        return True, selected_font
    
    # 方案3: 使用matplotlib内置的DejaVu字体 + 英文标签
    plt.rcParams['font.family'] = ['DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    print("⚠️ 未找到中文字体，使用英文标签")
    return False, "DejaVu Sans"

def test_font_display():
    """测试字体显示效果"""
    print("🧪 测试字体显示效果...")
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
    
    # 测试中文字符
    test_text = "人口密度分布图"
    ax1.text(0.5, 0.5, test_text, fontsize=16, ha='center', va='center', 
             transform=ax1.transAxes)
    ax1.set_title("中文字体测试")
    ax1.set_xlabel("X坐标")
    ax1.set_ylabel("Y坐标")
    
    # 测试图表
    x = np.random.randn(50)
    y = np.random.randn(50)
    colors = np.random.randn(50)
    
    scatter = ax2.scatter(x, y, c=colors, cmap='viridis', alpha=0.7)
    ax2.set_title("散点图测试 - 经济发展水平")
    ax2.set_xlabel("X坐标")
    ax2.set_ylabel("Y坐标")
    plt.colorbar(scatter, ax=ax2)
    
    plt.tight_layout()
    plt.savefig(f"{OUTPUT_DIR}/figures_ultimate/font_test_ultimate.png", 
                dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ 字体测试图已保存: {OUTPUT_DIR}/figures_ultimate/font_test_ultimate.png")

def create_ultimate_charts():
    """创建终极修复版图表"""
    print("🎨 开始创建终极修复版图表...")
    
    # 生成测试数据
    np.random.seed(42)
    coordinates = np.random.uniform(0, 10, (100, 2))
    
    # 使用英文标签确保显示
    feature_names = ['Population\nDensity', 'Economic\nDevelopment', 'Education\nLevel', 
                    'Environmental\nQuality', 'Transportation\nAccessibility']
    
    n_features = len(feature_names)
    data = np.zeros((len(coordinates), n_features))
    
    for i, (x, y) in enumerate(coordinates):
        center_dist = np.sqrt((x - 5)**2 + (y - 5)**2)
        data[i, 0] = 100 * np.exp(-center_dist/3) + np.random.normal(0, 10)
        data[i, 1] = 0.8 * data[i, 0] + 5 * x + np.random.normal(0, 15)
        data[i, 2] = 0.6 * data[i, 1] + 3 * y + np.random.normal(0, 12)
        data[i, 3] = 80 - 0.3 * data[i, 0] + np.random.normal(0, 8)
        road_dist = abs(x - y) + abs(x + y - 10)
        data[i, 4] = 60 - 2 * road_dist + 0.2 * data[i, 0] + np.random.normal(0, 10)
    
    data = np.maximum(data, 0)
    
    # 1. 原始数据分布图
    print("📊 生成原始数据分布图...")
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    axes = axes.ravel()
    
    for i in range(len(feature_names)):
        scatter = axes[i].scatter(coordinates[:, 0], coordinates[:, 1], 
                                c=data[:, i], cmap='viridis', s=50, alpha=0.7)
        axes[i].set_title(feature_names[i], fontsize=12, fontweight='bold')
        axes[i].set_xlabel('X Coordinate', fontsize=10)
        axes[i].set_ylabel('Y Coordinate', fontsize=10)
        plt.colorbar(scatter, ax=axes[i])
        axes[i].grid(True, alpha=0.3)
    
    if len(feature_names) < 6:
        axes[5].set_visible(False)
    
    plt.suptitle('Spatial Distribution of Original Data', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig(f"{OUTPUT_DIR}/figures_ultimate/spatial_data_distribution_ultimate.png", 
                dpi=300, bbox_inches='tight')
    plt.close()
    
    # 执行GWPCA
    print("🔍 执行GWPCA分析...")
    gwpca = GWPCA(n_components=3, bandwidth='adaptive', kernel='gaussian')
    X_transformed = gwpca.fit_transform(data, coordinates)
    
    # 2. 主成分得分分布
    print("📈 生成主成分得分分布图...")
    fig, axes = plt.subplots(1, 3, figsize=(15, 4))
    
    pc_labels = ['PC1 Scores', 'PC2 Scores', 'PC3 Scores']
    
    for i in range(3):
        scatter = axes[i].scatter(coordinates[:, 0], coordinates[:, 1], 
                                c=X_transformed[:, i], cmap='RdBu_r', s=50, alpha=0.7)
        axes[i].set_title(pc_labels[i], fontsize=12, fontweight='bold')
        axes[i].set_xlabel('X Coordinate', fontsize=10)
        axes[i].set_ylabel('Y Coordinate', fontsize=10)
        plt.colorbar(scatter, ax=axes[i])
        axes[i].grid(True, alpha=0.3)
    
    plt.suptitle('Spatial Distribution of Principal Component Scores', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig(f"{OUTPUT_DIR}/figures_ultimate/pc_scores_spatial_ultimate.png", 
                dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. 解释方差分布
    print("📊 生成解释方差分布图...")
    fig, axes = plt.subplots(1, 3, figsize=(15, 4))
    
    var_labels = ['PC1 Explained\nVariance Ratio', 'PC2 Explained\nVariance Ratio', 'PC3 Explained\nVariance Ratio']
    
    for i in range(3):
        scatter = axes[i].scatter(coordinates[:, 0], coordinates[:, 1], 
                                c=gwpca.local_explained_variance_ratio_[:, i], 
                                cmap='plasma', s=50, alpha=0.7)
        axes[i].set_title(var_labels[i], fontsize=12, fontweight='bold')
        axes[i].set_xlabel('X Coordinate', fontsize=10)
        axes[i].set_ylabel('Y Coordinate', fontsize=10)
        plt.colorbar(scatter, ax=axes[i])
        axes[i].grid(True, alpha=0.3)
    
    plt.suptitle('Spatial Distribution of Local Explained Variance Ratio', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig(f"{OUTPUT_DIR}/figures_ultimate/explained_variance_spatial_ultimate.png", 
                dpi=300, bbox_inches='tight')
    plt.close()
    
    # 4. 第一主成分载荷分布
    print("📈 生成第一主成分载荷分布图...")
    fig, axes = plt.subplots(2, 3, figsize=(15, 8))
    axes = axes.ravel()
    
    loading_labels = [f'{name}\nLoading' for name in feature_names]
    
    for i in range(len(feature_names)):
        loadings = gwpca.local_components_[:, 0, i]
        scatter = axes[i].scatter(coordinates[:, 0], coordinates[:, 1], 
                                c=loadings, cmap='RdBu_r', s=50, alpha=0.7)
        axes[i].set_title(loading_labels[i], fontsize=12, fontweight='bold')
        axes[i].set_xlabel('X Coordinate', fontsize=10)
        axes[i].set_ylabel('Y Coordinate', fontsize=10)
        plt.colorbar(scatter, ax=axes[i])
        axes[i].grid(True, alpha=0.3)
    
    if len(feature_names) < 6:
        axes[5].set_visible(False)
    
    plt.suptitle('Spatial Distribution of PC1 Loadings', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig(f"{OUTPUT_DIR}/figures_ultimate/pc1_loadings_spatial_ultimate.png", 
                dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 所有终极修复版图表已生成完成！")
    print(f"📁 保存位置: {OUTPUT_DIR}/figures_ultimate/")
    
    return gwpca, coordinates, data, feature_names

def main():
    """主函数"""
    print("🚀 启动终极字体修复方案")
    print("=" * 60)
    
    # 设置字体
    use_chinese, font_name = setup_ultimate_chinese_font()
    
    # 测试字体
    test_font_display()
    
    # 创建图表
    gwpca, coords, data, names = create_ultimate_charts()
    
    print("\n" + "=" * 60)
    print("🎯 终极字体修复完成！")
    print(f"📝 使用字体: {font_name}")
    print(f"🌐 中文支持: {'是' if use_chinese else '否（使用英文标签）'}")
    print(f"📊 生成图表: 4个高清PNG文件")
    print(f"📁 保存位置: {OUTPUT_DIR}/figures_ultimate/")
    
    print("\n💡 文件说明:")
    print("- font_test_ultimate.png: 字体测试图")
    print("- spatial_data_distribution_ultimate.png: 原始数据分布")
    print("- pc_scores_spatial_ultimate.png: 主成分得分分布")
    print("- explained_variance_spatial_ultimate.png: 解释方差分布")
    print("- pc1_loadings_spatial_ultimate.png: 载荷分布")

if __name__ == "__main__":
    main()
