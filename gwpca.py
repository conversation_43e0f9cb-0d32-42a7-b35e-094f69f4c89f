"""
地理加权主成分分析 (Geographically Weighted Principal Component Analysis, GWPCA)

这个模块实现了GWPCA算法，它是传统PCA的空间扩展，考虑了地理位置的影响，
允许在不同的地理位置进行局部主成分分析。

主要特性：
- 基于地理距离的空间权重计算
- 局部主成分分析
- 多种空间权重函数（高斯、双平方、指数等）
- 自适应或固定带宽选择
- 空间变异性分析
"""

import numpy as np
import pandas as pd
from scipy.linalg import eigh
from scipy.spatial.distance import cdist
from sklearn.preprocessing import StandardScaler
import warnings


class GWPCA:
    """
    地理加权主成分分析类

    参数:
    --------
    n_components : int, optional (default=None)
        要保留的主成分数量。如果为None，则保留所有成分
    bandwidth : float, optional (default='adaptive')
        空间权重函数的带宽。如果为'adaptive'，则自动选择最优带宽
    kernel : str, optional (default='gaussian')
        空间权重函数类型：'gaussian', 'bisquare', 'exponential', 'tricube'
    standardize : bool, optional (default=True)
        是否对数据进行标准化
    center : bool, optional (default=True)
        是否对数据进行中心化
    """

    def __init__(self, n_components=None, bandwidth='adaptive', kernel='gaussian',
                 standardize=True, center=True):
        self.n_components = n_components
        self.bandwidth = bandwidth
        self.kernel = kernel
        self.standardize = standardize
        self.center = center

        # 拟合后的属性
        self.coordinates_ = None
        self.local_components_ = None  # 每个位置的局部主成分
        self.local_eigenvalues_ = None  # 每个位置的局部特征值
        self.local_explained_variance_ratio_ = None  # 每个位置的解释方差比例
        self.bandwidth_ = None  # 实际使用的带宽
        self.mean_ = None
        self.std_ = None
        
    def fit(self, X, coordinates):
        """
        拟合地理加权PCA模型

        参数:
        --------
        X : array-like, shape (n_samples, n_features)
            训练数据
        coordinates : array-like, shape (n_samples, 2)
            每个样本的地理坐标 (x, y)

        返回:
        --------
        self : object
        """
        X = np.asarray(X)
        coordinates = np.asarray(coordinates)
        n_samples, n_features = X.shape

        if coordinates.shape[0] != n_samples:
            raise ValueError("坐标数量必须与样本数量相等")
        if coordinates.shape[1] != 2:
            raise ValueError("坐标必须是二维的 (x, y)")

        self.coordinates_ = coordinates

        # 数据预处理
        X_processed = self._preprocess_data(X)

        # 确定带宽
        if self.bandwidth == 'adaptive':
            self.bandwidth_ = self._select_bandwidth(coordinates, X_processed)
        else:
            self.bandwidth_ = self.bandwidth

        # 确定保留的成分数量
        if self.n_components is None:
            self.n_components = min(n_samples, n_features)
        else:
            self.n_components = min(self.n_components, min(n_samples, n_features))

        # 为每个位置计算局部PCA
        self.local_components_ = np.zeros((n_samples, self.n_components, n_features))
        self.local_eigenvalues_ = np.zeros((n_samples, self.n_components))
        self.local_explained_variance_ratio_ = np.zeros((n_samples, self.n_components))

        for i in range(n_samples):
            # 计算空间权重
            weights = self._compute_spatial_weights(coordinates[i], coordinates)

            # 局部加权PCA
            local_cov = self._compute_local_covariance(X_processed, weights)
            eigenvalues, eigenvectors = eigh(local_cov)

            # 按特征值降序排列
            idx = np.argsort(eigenvalues)[::-1]
            eigenvalues = eigenvalues[idx]
            eigenvectors = eigenvectors[:, idx]

            # 保存局部结果
            self.local_eigenvalues_[i] = eigenvalues[:self.n_components]
            self.local_components_[i] = eigenvectors[:, :self.n_components].T
            total_variance = np.sum(eigenvalues)
            if total_variance > 0:
                self.local_explained_variance_ratio_[i] = eigenvalues[:self.n_components] / total_variance

        return self
    
    def transform(self, X, coordinates=None):
        """
        将数据转换到局部主成分空间

        参数:
        --------
        X : array-like, shape (n_samples, n_features)
            要转换的数据
        coordinates : array-like, shape (n_samples, 2), optional
            新数据的坐标。如果为None，使用训练时的坐标

        返回:
        --------
        X_transformed : array, shape (n_samples, n_components)
            转换后的数据
        """
        if self.local_components_ is None:
            raise ValueError("模型尚未拟合。请先调用fit方法。")

        X = np.asarray(X)
        X_processed = self._preprocess_data(X, fit=False)

        if coordinates is None:
            coordinates = self.coordinates_
        else:
            coordinates = np.asarray(coordinates)

        n_samples = X.shape[0]
        X_transformed = np.zeros((n_samples, self.n_components))

        for i in range(n_samples):
            # 找到最近的训练点或插值
            if coordinates is self.coordinates_:
                # 使用训练时的局部成分
                components = self.local_components_[i]
            else:
                # 为新位置插值局部成分
                components = self._interpolate_components(coordinates[i])

            # 转换数据
            X_transformed[i] = np.dot(X_processed[i], components.T)

        return X_transformed

    def fit_transform(self, X, coordinates):
        """
        拟合模型并转换数据

        参数:
        --------
        X : array-like, shape (n_samples, n_features)
            训练数据
        coordinates : array-like, shape (n_samples, 2)
            地理坐标

        返回:
        --------
        X_transformed : array, shape (n_samples, n_components)
            转换后的数据
        """
        return self.fit(X, coordinates).transform(X)
    
    def _compute_spatial_weights(self, target_coord, all_coords):
        """
        计算空间权重

        参数:
        --------
        target_coord : array-like, shape (2,)
            目标点坐标
        all_coords : array-like, shape (n_samples, 2)
            所有样本坐标

        返回:
        --------
        weights : array, shape (n_samples,)
            空间权重
        """
        # 计算距离
        distances = cdist([target_coord], all_coords)[0]

        # 根据核函数计算权重
        if self.kernel == 'gaussian':
            weights = np.exp(-(distances**2) / (2 * self.bandwidth_**2))
        elif self.kernel == 'bisquare':
            normalized_dist = distances / self.bandwidth_
            weights = np.where(normalized_dist < 1,
                             (1 - normalized_dist**2)**2, 0)
        elif self.kernel == 'exponential':
            weights = np.exp(-distances / self.bandwidth_)
        elif self.kernel == 'tricube':
            normalized_dist = distances / self.bandwidth_
            weights = np.where(normalized_dist < 1,
                             (1 - normalized_dist**3)**3, 0)
        else:
            raise ValueError(f"不支持的核函数: {self.kernel}")

        return weights

    def _select_bandwidth(self, coordinates, X):
        """
        自适应带宽选择（使用交叉验证）

        参数:
        --------
        coordinates : array-like, shape (n_samples, 2)
            样本坐标
        X : array-like, shape (n_samples, n_features)
            预处理后的数据

        返回:
        --------
        optimal_bandwidth : float
            最优带宽
        """
        n_samples = coordinates.shape[0]

        # 计算距离矩阵
        dist_matrix = cdist(coordinates, coordinates)

        # 候选带宽（基于最近邻距离）
        k_neighbors = min(10, n_samples // 4)  # 使用10个最近邻或样本数的1/4
        knn_distances = np.sort(dist_matrix, axis=1)[:, k_neighbors]
        bandwidth_candidates = np.percentile(knn_distances, [25, 50, 75, 90])

        best_bandwidth = bandwidth_candidates[1]  # 默认使用中位数
        best_score = -np.inf

        # 简化的交叉验证
        for bandwidth in bandwidth_candidates:
            score = self._evaluate_bandwidth(coordinates, X, bandwidth)
            if score > best_score:
                best_score = score
                best_bandwidth = bandwidth

        return best_bandwidth

    def _evaluate_bandwidth(self, coordinates, X, bandwidth):
        """
        评估带宽质量（使用局部方差解释比例）

        参数:
        --------
        coordinates : array-like, shape (n_samples, 2)
            样本坐标
        X : array-like, shape (n_samples, n_features)
            预处理后的数据
        bandwidth : float
            待评估的带宽

        返回:
        --------
        score : float
            带宽质量得分
        """
        n_samples = X.shape[0]
        local_scores = []

        # 随机选择一些点进行评估
        eval_indices = np.random.choice(n_samples, min(20, n_samples), replace=False)

        for i in eval_indices:
            # 计算空间权重
            distances = cdist([coordinates[i]], coordinates)[0]
            if self.kernel == 'gaussian':
                weights = np.exp(-(distances**2) / (2 * bandwidth**2))
            else:
                weights = np.exp(-distances / bandwidth)  # 简化评估

            # 计算局部协方差矩阵
            local_cov = self._compute_local_covariance(X, weights)
            eigenvalues = np.linalg.eigvals(local_cov)
            eigenvalues = eigenvalues[eigenvalues > 0]  # 只考虑正特征值

            if len(eigenvalues) > 1:
                # 计算第一主成分的解释方差比例
                explained_ratio = eigenvalues[0] / np.sum(eigenvalues)
                local_scores.append(explained_ratio)

        return np.mean(local_scores) if local_scores else 0.0

    def _compute_local_covariance(self, X, weights):
        """
        计算局部加权协方差矩阵

        参数:
        --------
        X : array-like, shape (n_samples, n_features)
            预处理后的数据
        weights : array-like, shape (n_samples,)
            空间权重

        返回:
        --------
        cov_matrix : array, shape (n_features, n_features)
            局部协方差矩阵
        """
        # 标准化权重
        weights = weights / np.sum(weights)

        # 计算加权协方差矩阵
        # Cov = X^T * W * X
        W = np.diag(weights)
        cov_matrix = X.T @ W @ X

        return cov_matrix

    def _interpolate_components(self, target_coord):
        """
        为新位置插值局部主成分

        参数:
        --------
        target_coord : array-like, shape (2,)
            目标位置坐标

        返回:
        --------
        interpolated_components : array, shape (n_components, n_features)
            插值得到的局部主成分
        """
        # 计算到所有训练点的权重
        weights = self._compute_spatial_weights(target_coord, self.coordinates_)

        # 权重归一化
        weights = weights / np.sum(weights)

        # 加权平均局部成分
        interpolated_components = np.zeros((self.n_components, self.local_components_.shape[2]))
        for i in range(len(weights)):
            interpolated_components += weights[i] * self.local_components_[i]

        return interpolated_components

    def _preprocess_data(self, X, fit=True):
        """数据预处理：中心化和标准化"""
        X = X.copy()

        if fit:
            if self.center:
                self.mean_ = np.mean(X, axis=0)
            else:
                self.mean_ = np.zeros(X.shape[1])

            if self.standardize:
                X_centered = X - self.mean_
                self.std_ = np.std(X_centered, axis=0)
                # 避免除零
                self.std_[self.std_ == 0] = 1.0
            else:
                self.std_ = np.ones(X.shape[1])

        # 应用预处理
        X = X - self.mean_
        if self.standardize:
            X = X / self.std_

        return X

    def get_local_summary(self, location_index=None):
        """
        获取局部主成分摘要信息

        参数:
        --------
        location_index : int, optional
            位置索引。如果为None，返回所有位置的平均摘要

        返回:
        --------
        summary : pandas.DataFrame
            局部主成分摘要
        """
        if self.local_components_ is None:
            raise ValueError("模型尚未拟合。请先调用fit方法。")

        if location_index is not None:
            # 返回特定位置的摘要
            eigenvalues = self.local_eigenvalues_[location_index]
            explained_variance_ratio = self.local_explained_variance_ratio_[location_index]
            cumulative_variance_ratio = np.cumsum(explained_variance_ratio)

            summary = pd.DataFrame({
                '主成分': [f'PC{i+1}' for i in range(len(eigenvalues))],
                '特征值': eigenvalues,
                '解释方差比例': explained_variance_ratio,
                '累积解释方差比例': cumulative_variance_ratio
            })
        else:
            # 返回所有位置的平均摘要
            mean_eigenvalues = np.mean(self.local_eigenvalues_, axis=0)
            mean_explained_variance_ratio = np.mean(self.local_explained_variance_ratio_, axis=0)
            cumulative_variance_ratio = np.cumsum(mean_explained_variance_ratio)

            summary = pd.DataFrame({
                '主成分': [f'PC{i+1}' for i in range(len(mean_eigenvalues))],
                '平均特征值': mean_eigenvalues,
                '平均解释方差比例': mean_explained_variance_ratio,
                '累积解释方差比例': cumulative_variance_ratio
            })

        return summary

    def get_spatial_variability(self):
        """
        计算主成分的空间变异性

        返回:
        --------
        variability : dict
            包含各主成分空间变异性指标的字典
        """
        if self.local_components_ is None:
            raise ValueError("模型尚未拟合。请先调用fit方法。")

        variability = {}

        for pc in range(self.n_components):
            # 计算每个主成分的空间变异性
            eigenvalue_var = np.var(self.local_eigenvalues_[:, pc])
            explained_var_var = np.var(self.local_explained_variance_ratio_[:, pc])

            # 计算主成分载荷的空间变异性
            loadings_var = np.var(self.local_components_[:, pc, :], axis=0)
            mean_loadings_var = np.mean(loadings_var)

            variability[f'PC{pc+1}'] = {
                '特征值方差': eigenvalue_var,
                '解释方差比例方差': explained_var_var,
                '平均载荷方差': mean_loadings_var
            }

        return variability

    def predict_at_location(self, new_coordinates, X_new):
        """
        在新位置进行预测

        参数:
        --------
        new_coordinates : array-like, shape (n_new_samples, 2)
            新位置的坐标
        X_new : array-like, shape (n_new_samples, n_features)
            新位置的数据

        返回:
        --------
        predictions : array, shape (n_new_samples, n_components)
            新位置的主成分得分
        """
        if self.local_components_ is None:
            raise ValueError("模型尚未拟合。请先调用fit方法。")

        new_coordinates = np.asarray(new_coordinates)
        X_new = np.asarray(X_new)

        # 预处理新数据
        X_new_processed = self._preprocess_data(X_new, fit=False)

        return self.transform(X_new_processed, new_coordinates)
