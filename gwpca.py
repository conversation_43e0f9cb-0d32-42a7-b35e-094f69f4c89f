"""
广义加权主成分分析 (Generalized Weighted Principal Component Analysis, GWPCA)

这个模块实现了GWPCA算法，它是传统PCA的扩展，允许对不同的观测值和变量赋予不同的权重。

主要特性：
- 支持观测权重和变量权重
- 处理缺失值
- 提供多种标准化选项
- 计算主成分得分和载荷
"""

import numpy as np
import pandas as pd
from scipy.linalg import eigh
from sklearn.preprocessing import StandardScaler
import warnings


class GWPCA:
    """
    广义加权主成分分析类
    
    参数:
    --------
    n_components : int, optional (default=None)
        要保留的主成分数量。如果为None，则保留所有成分
    standardize : bool, optional (default=True)
        是否对数据进行标准化
    center : bool, optional (default=True)
        是否对数据进行中心化
    """
    
    def __init__(self, n_components=None, standardize=True, center=True):
        self.n_components = n_components
        self.standardize = standardize
        self.center = center
        
        # 拟合后的属性
        self.components_ = None
        self.explained_variance_ = None
        self.explained_variance_ratio_ = None
        self.singular_values_ = None
        self.mean_ = None
        self.std_ = None
        self.eigenvalues_ = None
        self.eigenvectors_ = None
        
    def fit(self, X, sample_weights=None, feature_weights=None):
        """
        拟合GWPCA模型
        
        参数:
        --------
        X : array-like, shape (n_samples, n_features)
            训练数据
        sample_weights : array-like, shape (n_samples,), optional
            样本权重。如果为None，则所有样本权重相等
        feature_weights : array-like, shape (n_features,), optional
            特征权重。如果为None，则所有特征权重相等
            
        返回:
        --------
        self : object
        """
        X = np.asarray(X)
        n_samples, n_features = X.shape
        
        # 设置默认权重
        if sample_weights is None:
            sample_weights = np.ones(n_samples)
        else:
            sample_weights = np.asarray(sample_weights)
            
        if feature_weights is None:
            feature_weights = np.ones(n_features)
        else:
            feature_weights = np.asarray(feature_weights)
            
        # 验证权重
        self._validate_weights(sample_weights, feature_weights, n_samples, n_features)
        
        # 标准化权重
        sample_weights = sample_weights / np.sum(sample_weights) * n_samples
        feature_weights = feature_weights / np.sum(feature_weights) * n_features
        
        # 数据预处理
        X_processed = self._preprocess_data(X, sample_weights)
        
        # 计算加权协方差矩阵
        cov_matrix = self._compute_weighted_covariance(X_processed, sample_weights, feature_weights)
        
        # 特征值分解
        eigenvalues, eigenvectors = eigh(cov_matrix)
        
        # 按特征值降序排列
        idx = np.argsort(eigenvalues)[::-1]
        eigenvalues = eigenvalues[idx]
        eigenvectors = eigenvectors[:, idx]
        
        # 确定保留的成分数量
        if self.n_components is None:
            self.n_components = min(n_samples, n_features)
        else:
            self.n_components = min(self.n_components, min(n_samples, n_features))
            
        # 保存结果
        self.eigenvalues_ = eigenvalues[:self.n_components]
        self.eigenvectors_ = eigenvectors[:, :self.n_components]
        self.components_ = self.eigenvectors_.T
        self.explained_variance_ = self.eigenvalues_
        self.explained_variance_ratio_ = self.eigenvalues_ / np.sum(eigenvalues)
        self.singular_values_ = np.sqrt(self.eigenvalues_)
        
        return self
    
    def transform(self, X):
        """
        将数据转换到主成分空间
        
        参数:
        --------
        X : array-like, shape (n_samples, n_features)
            要转换的数据
            
        返回:
        --------
        X_transformed : array, shape (n_samples, n_components)
            转换后的数据
        """
        if self.components_ is None:
            raise ValueError("模型尚未拟合。请先调用fit方法。")
            
        X = np.asarray(X)
        X_processed = self._preprocess_data(X, fit=False)
        
        return np.dot(X_processed, self.components_.T)
    
    def fit_transform(self, X, sample_weights=None, feature_weights=None):
        """
        拟合模型并转换数据
        
        参数:
        --------
        X : array-like, shape (n_samples, n_features)
            训练数据
        sample_weights : array-like, shape (n_samples,), optional
            样本权重
        feature_weights : array-like, shape (n_features,), optional
            特征权重
            
        返回:
        --------
        X_transformed : array, shape (n_samples, n_components)
            转换后的数据
        """
        return self.fit(X, sample_weights, feature_weights).transform(X)
    
    def inverse_transform(self, X_transformed):
        """
        将主成分空间的数据转换回原始空间
        
        参数:
        --------
        X_transformed : array-like, shape (n_samples, n_components)
            主成分空间的数据
            
        返回:
        --------
        X_original : array, shape (n_samples, n_features)
            原始空间的数据
        """
        if self.components_ is None:
            raise ValueError("模型尚未拟合。请先调用fit方法。")
            
        X_transformed = np.asarray(X_transformed)
        X_reconstructed = np.dot(X_transformed, self.components_)
        
        # 反向预处理
        if self.std_ is not None:
            X_reconstructed = X_reconstructed * self.std_
        if self.mean_ is not None:
            X_reconstructed = X_reconstructed + self.mean_
            
        return X_reconstructed
    
    def _validate_weights(self, sample_weights, feature_weights, n_samples, n_features):
        """验证权重的有效性"""
        if len(sample_weights) != n_samples:
            raise ValueError(f"样本权重长度 ({len(sample_weights)}) 与样本数量 ({n_samples}) 不匹配")
        if len(feature_weights) != n_features:
            raise ValueError(f"特征权重长度 ({len(feature_weights)}) 与特征数量 ({n_features}) 不匹配")
        if np.any(sample_weights < 0):
            raise ValueError("样本权重不能为负数")
        if np.any(feature_weights < 0):
            raise ValueError("特征权重不能为负数")
        if np.sum(sample_weights) == 0:
            raise ValueError("样本权重总和不能为零")
        if np.sum(feature_weights) == 0:
            raise ValueError("特征权重总和不能为零")
    
    def _preprocess_data(self, X, sample_weights=None, fit=True):
        """数据预处理：中心化和标准化"""
        X = X.copy()
        
        if fit:
            if self.center:
                if sample_weights is not None:
                    # 加权均值
                    self.mean_ = np.average(X, axis=0, weights=sample_weights)
                else:
                    self.mean_ = np.mean(X, axis=0)
            else:
                self.mean_ = np.zeros(X.shape[1])
                
            if self.standardize:
                X_centered = X - self.mean_
                if sample_weights is not None:
                    # 加权标准差
                    variance = np.average(X_centered**2, axis=0, weights=sample_weights)
                    self.std_ = np.sqrt(variance)
                else:
                    self.std_ = np.std(X_centered, axis=0)
                # 避免除零
                self.std_[self.std_ == 0] = 1.0
            else:
                self.std_ = np.ones(X.shape[1])
        
        # 应用预处理
        X = X - self.mean_
        if self.standardize:
            X = X / self.std_
            
        return X
    
    def _compute_weighted_covariance(self, X, sample_weights, feature_weights):
        """计算加权协方差矩阵"""
        n_samples, n_features = X.shape
        
        # 创建权重矩阵
        W_samples = np.diag(sample_weights)
        W_features = np.diag(feature_weights)
        
        # 计算加权协方差矩阵
        # Cov = (1/n) * X^T * W_samples * X * W_features
        cov_matrix = (1/n_samples) * X.T @ W_samples @ X @ W_features
        
        return cov_matrix
    
    def get_feature_importance(self):
        """
        获取特征重要性（基于主成分载荷的平方和）
        
        返回:
        --------
        importance : array, shape (n_features,)
            特征重要性得分
        """
        if self.components_ is None:
            raise ValueError("模型尚未拟合。请先调用fit方法。")
            
        # 计算每个特征在所有主成分上的载荷平方和
        importance = np.sum(self.components_**2, axis=0)
        return importance
    
    def get_component_summary(self):
        """
        获取主成分摘要信息
        
        返回:
        --------
        summary : pandas.DataFrame
            包含特征值、解释方差比例和累积解释方差比例的摘要
        """
        if self.components_ is None:
            raise ValueError("模型尚未拟合。请先调用fit方法。")
            
        cumulative_variance_ratio = np.cumsum(self.explained_variance_ratio_)
        
        summary = pd.DataFrame({
            '主成分': [f'PC{i+1}' for i in range(len(self.eigenvalues_))],
            '特征值': self.eigenvalues_,
            '解释方差比例': self.explained_variance_ratio_,
            '累积解释方差比例': cumulative_variance_ratio
        })
        
        return summary
