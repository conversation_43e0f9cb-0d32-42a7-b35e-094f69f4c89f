"""
地理加权主成分分析 (GWPCA) 演示文件

这个文件展示了如何使用GWPCA进行地理空间数据分析，包括：
- 生成模拟地理数据
- 执行GWPCA分析
- 可视化结果
- 解释空间变异性
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from gwpca import GWPCA
import warnings
import os
import json

# 设置中文字体和样式
import matplotlib.font_manager as fm

# 尝试多种中文字体设置方案
def setup_chinese_font():
    """设置中文字体"""
    # 方案1：尝试系统中文字体
    chinese_fonts = [
        'Microsoft YaHei',  # 微软雅黑
        'SimHei',          # 黑体
        'KaiTi',           # 楷体
        'SimSun',          # 宋体
        'FangSong',        # 仿宋
        'STSong',          # 华文宋体
        'STKaiti',         # 华文楷体
        'STHeiti',         # 华文黑体
        'Arial Unicode MS', # Arial Unicode MS
        'DejaVu Sans'      # 备用字体
    ]

    # 检查可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]

    for font in chinese_fonts:
        if font in available_fonts:
            plt.rcParams['font.sans-serif'] = [font]
            print(f"✅ 使用字体: {font}")
            break
    else:
        # 如果没有找到中文字体，使用英文标签
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
        print("⚠️ 未找到中文字体，将使用英文标签")
        return False

    plt.rcParams['axes.unicode_minus'] = False
    return True

# 设置字体
use_chinese = setup_chinese_font()
plt.ioff()  # 关闭交互模式，不显示图形
sns.set_style("whitegrid")
warnings.filterwarnings('ignore')

# 创建输出目录
OUTPUT_DIR = "output/demo"
os.makedirs(OUTPUT_DIR, exist_ok=True)
os.makedirs(f"{OUTPUT_DIR}/data", exist_ok=True)
os.makedirs(f"{OUTPUT_DIR}/figures", exist_ok=True)
os.makedirs(f"{OUTPUT_DIR}/results", exist_ok=True)


def generate_realistic_spatial_data(n_points=100, region_size=10):
    """
    生成更真实的地理空间数据
    
    参数:
    --------
    n_points : int
        样本点数量
    region_size : float
        研究区域大小
        
    返回:
    --------
    coordinates : array, shape (n_points, 2)
        地理坐标
    data : array, shape (n_points, n_features)
        多变量数据
    feature_names : list
        特征名称
    """
    np.random.seed(42)
    
    # 生成随机分布的坐标点
    coordinates = np.random.uniform(0, region_size, (n_points, 2))
    
    # 特征名称
    feature_names = ['人口密度', '经济发展水平', '教育水平', '环境质量', '交通便利性']
    n_features = len(feature_names)
    
    data = np.zeros((n_points, n_features))
    
    for i, (x, y) in enumerate(coordinates):
        # 人口密度：城市中心（5,5）附近密度高
        center_dist = np.sqrt((x - region_size/2)**2 + (y - region_size/2)**2)
        data[i, 0] = 100 * np.exp(-center_dist/3) + np.random.normal(0, 10)
        
        # 经济发展水平：与人口密度正相关，但有东西梯度
        data[i, 1] = 0.8 * data[i, 0] + 5 * x + np.random.normal(0, 15)
        
        # 教育水平：与经济发展相关，但有南北差异
        data[i, 2] = 0.6 * data[i, 1] + 3 * y + np.random.normal(0, 12)
        
        # 环境质量：远离中心区域质量更好
        data[i, 3] = 80 - 0.3 * data[i, 0] + np.random.normal(0, 8)
        
        # 交通便利性：主要道路（对角线）附近更便利
        road_dist = abs(x - y) + abs(x + y - region_size)
        data[i, 4] = 60 - 2 * road_dist + 0.2 * data[i, 0] + np.random.normal(0, 10)
    
    # 确保数据为正值
    data = np.maximum(data, 0)
    
    return coordinates, data, feature_names


def visualize_spatial_data(coordinates, data, feature_names, title="原始数据空间分布"):
    """可视化空间数据分布"""
    # 根据字体可用性设置标签
    if use_chinese:
        x_label, y_label = 'X坐标', 'Y坐标'
        title_text = title
    else:
        x_label, y_label = 'X Coordinate', 'Y Coordinate'
        title_text = "Spatial Distribution of Original Data"
        # 使用英文特征名
        feature_names_en = ['Population Density', 'Economic Development', 'Education Level',
                           'Environmental Quality', 'Transportation Accessibility']
        feature_names = feature_names_en

    n_features = len(feature_names)
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    axes = axes.ravel()

    for i in range(n_features):
        scatter = axes[i].scatter(coordinates[:, 0], coordinates[:, 1],
                                c=data[:, i], cmap='viridis', s=50, alpha=0.7)
        axes[i].set_title(f'{feature_names[i]}')
        axes[i].set_xlabel(x_label)
        axes[i].set_ylabel(y_label)
        plt.colorbar(scatter, ax=axes[i])

    # 隐藏多余的子图
    if n_features < 6:
        axes[5].set_visible(False)

    plt.suptitle(title_text, fontsize=16)
    plt.tight_layout()

    # 保存图片
    plt.savefig(f"{OUTPUT_DIR}/figures/spatial_data_distribution.png", dpi=300, bbox_inches='tight')
    plt.close()

    # 保存数据
    data_df = pd.DataFrame(data, columns=feature_names)
    coords_df = pd.DataFrame(coordinates, columns=['X坐标', 'Y坐标'])
    combined_df = pd.concat([coords_df, data_df], axis=1)
    combined_df.to_csv(f"{OUTPUT_DIR}/data/original_data.csv", index=False, encoding='utf-8-sig')

    print(f"✅ 原始数据已保存到: {OUTPUT_DIR}/data/original_data.csv")
    print(f"✅ 空间分布图已保存到: {OUTPUT_DIR}/figures/spatial_data_distribution.png")


def visualize_gwpca_results(gwpca, coordinates, X_transformed, feature_names):
    """可视化GWPCA结果"""

    # 根据字体可用性设置标签
    if use_chinese:
        x_label, y_label = 'X坐标', 'Y坐标'
        pc_titles = [f'第{i+1}主成分得分' for i in range(3)]
        main_title1 = '主成分得分的空间分布'
        main_title2 = '局部解释方差比例的空间分布'
        main_title3 = '第一主成分载荷的空间分布'
        var_titles = [f'第{i+1}主成分解释方差比例' for i in range(3)]
        loading_titles = [f'{name}的载荷' for name in feature_names]
    else:
        x_label, y_label = 'X Coordinate', 'Y Coordinate'
        pc_titles = [f'PC{i+1} Scores' for i in range(3)]
        main_title1 = 'Spatial Distribution of PC Scores'
        main_title2 = 'Spatial Distribution of Local Explained Variance Ratio'
        main_title3 = 'Spatial Distribution of PC1 Loadings'
        var_titles = [f'PC{i+1} Explained Variance Ratio' for i in range(3)]
        feature_names_en = ['Population Density', 'Economic Development', 'Education Level',
                           'Environmental Quality', 'Transportation Accessibility']
        loading_titles = [f'{name} Loading' for name in feature_names_en]
        feature_names = feature_names_en

    # 1. 主成分得分的空间分布
    fig, axes = plt.subplots(1, 3, figsize=(15, 4))

    for i in range(3):
        scatter = axes[i].scatter(coordinates[:, 0], coordinates[:, 1],
                                c=X_transformed[:, i], cmap='RdBu_r', s=50, alpha=0.7)
        axes[i].set_title(pc_titles[i])
        axes[i].set_xlabel(x_label)
        axes[i].set_ylabel(y_label)
        plt.colorbar(scatter, ax=axes[i])

    plt.suptitle(main_title1, fontsize=16)
    plt.tight_layout()
    plt.savefig(f"{OUTPUT_DIR}/figures/pc_scores_spatial.png", dpi=300, bbox_inches='tight')
    plt.close()

    # 2. 局部解释方差比例的空间变化
    fig, axes = plt.subplots(1, 3, figsize=(15, 4))

    for i in range(3):
        scatter = axes[i].scatter(coordinates[:, 0], coordinates[:, 1],
                                c=gwpca.local_explained_variance_ratio_[:, i],
                                cmap='plasma', s=50, alpha=0.7)
        axes[i].set_title(var_titles[i])
        axes[i].set_xlabel(x_label)
        axes[i].set_ylabel(y_label)
        plt.colorbar(scatter, ax=axes[i])

    plt.suptitle(main_title2, fontsize=16)
    plt.tight_layout()
    plt.savefig(f"{OUTPUT_DIR}/figures/explained_variance_spatial.png", dpi=300, bbox_inches='tight')
    plt.close()

    # 3. 第一主成分载荷的空间变化
    fig, axes = plt.subplots(2, 3, figsize=(15, 8))
    axes = axes.ravel()

    for i in range(len(feature_names)):
        loadings = gwpca.local_components_[:, 0, i]  # 第一主成分的载荷
        scatter = axes[i].scatter(coordinates[:, 0], coordinates[:, 1],
                                c=loadings, cmap='RdBu_r', s=50, alpha=0.7)
        axes[i].set_title(loading_titles[i])
        axes[i].set_xlabel(x_label)
        axes[i].set_ylabel(y_label)
        plt.colorbar(scatter, ax=axes[i])

    # 隐藏多余的子图
    if len(feature_names) < 6:
        axes[5].set_visible(False)

    plt.suptitle(main_title3, fontsize=16)
    plt.tight_layout()
    plt.savefig(f"{OUTPUT_DIR}/figures/pc1_loadings_spatial.png", dpi=300, bbox_inches='tight')
    plt.close()

    # 保存GWPCA结果数据
    # 主成分得分
    pc_scores_df = pd.DataFrame(X_transformed, columns=[f'PC{i+1}' for i in range(X_transformed.shape[1])])
    coords_df = pd.DataFrame(coordinates, columns=['X坐标', 'Y坐标'])
    scores_with_coords = pd.concat([coords_df, pc_scores_df], axis=1)
    scores_with_coords.to_csv(f"{OUTPUT_DIR}/data/pc_scores.csv", index=False, encoding='utf-8-sig')

    # 局部解释方差比例
    local_var_df = pd.DataFrame(gwpca.local_explained_variance_ratio_,
                               columns=[f'PC{i+1}_解释方差比例' for i in range(gwpca.local_explained_variance_ratio_.shape[1])])
    var_with_coords = pd.concat([coords_df, local_var_df], axis=1)
    var_with_coords.to_csv(f"{OUTPUT_DIR}/data/local_explained_variance.csv", index=False, encoding='utf-8-sig')

    # 第一主成分载荷
    pc1_loadings_df = pd.DataFrame(gwpca.local_components_[:, 0, :], columns=feature_names)
    loadings_with_coords = pd.concat([coords_df, pc1_loadings_df], axis=1)
    loadings_with_coords.to_csv(f"{OUTPUT_DIR}/data/pc1_loadings.csv", index=False, encoding='utf-8-sig')

    print(f"✅ GWPCA结果已保存:")
    print(f"   - 主成分得分: {OUTPUT_DIR}/data/pc_scores.csv")
    print(f"   - 局部解释方差: {OUTPUT_DIR}/data/local_explained_variance.csv")
    print(f"   - 第一主成分载荷: {OUTPUT_DIR}/data/pc1_loadings.csv")
    print(f"   - 图片已保存到: {OUTPUT_DIR}/figures/")


def analyze_spatial_patterns(gwpca, coordinates, feature_names):
    """分析空间模式"""
    print("=== 空间模式分析 ===\n")

    # 1. 整体摘要
    print("1. 局部主成分摘要（所有位置平均）:")
    summary = gwpca.get_local_summary()
    print(summary.round(4))
    print()

    # 保存整体摘要
    summary.to_csv(f"{OUTPUT_DIR}/results/local_pca_summary.csv", index=False, encoding='utf-8-sig')

    # 2. 空间变异性
    print("2. 主成分的空间变异性:")
    variability = gwpca.get_spatial_variability()
    variability_data = []
    for pc_name, metrics in variability.items():
        print(f"   {pc_name}:")
        row = {'主成分': pc_name}
        for metric_name, value in metrics.items():
            print(f"     - {metric_name}: {value:.6f}")
            row[metric_name] = value
        variability_data.append(row)
    print()

    # 保存空间变异性数据
    variability_df = pd.DataFrame(variability_data)
    variability_df.to_csv(f"{OUTPUT_DIR}/results/spatial_variability.csv", index=False, encoding='utf-8-sig')

    # 3. 特定位置的详细分析
    print("3. 特定位置的主成分分析:")

    # 选择几个代表性位置
    center_idx = np.argmin(np.sum((coordinates - np.array([5, 5]))**2, axis=1))
    corner_idx = np.argmin(np.sum((coordinates - np.array([0, 0]))**2, axis=1))
    edge_idx = np.argmin(np.sum((coordinates - np.array([10, 5]))**2, axis=1))

    locations = [
        (center_idx, "中心区域"),
        (corner_idx, "边角区域"),
        (edge_idx, "边缘区域")
    ]

    location_analysis = []
    for idx, location_name in locations:
        print(f"\n   {location_name} (坐标: {coordinates[idx]}):")
        local_summary = gwpca.get_local_summary(idx)
        print(f"     第一主成分解释方差比例: {local_summary.iloc[0]['解释方差比例']:.4f}")

        # 显示第一主成分的载荷
        loadings = gwpca.local_components_[idx, 0, :]
        print("     第一主成分载荷:")

        location_data = {
            '位置名称': location_name,
            'X坐标': coordinates[idx][0],
            'Y坐标': coordinates[idx][1],
            '第一主成分解释方差比例': local_summary.iloc[0]['解释方差比例']
        }

        for j, (feature, loading) in enumerate(zip(feature_names, loadings)):
            print(f"       {feature}: {loading:.4f}")
            location_data[f'{feature}_载荷'] = loading

        location_analysis.append(location_data)

    # 保存特定位置分析
    location_df = pd.DataFrame(location_analysis)
    location_df.to_csv(f"{OUTPUT_DIR}/results/specific_locations_analysis.csv", index=False, encoding='utf-8-sig')

    print(f"\n✅ 空间模式分析结果已保存:")
    print(f"   - 局部PCA摘要: {OUTPUT_DIR}/results/local_pca_summary.csv")
    print(f"   - 空间变异性: {OUTPUT_DIR}/results/spatial_variability.csv")
    print(f"   - 特定位置分析: {OUTPUT_DIR}/results/specific_locations_analysis.csv")


def compare_global_vs_local_pca(coordinates, data, feature_names):
    """比较全局PCA和地理加权PCA"""
    print("=== 全局PCA vs 地理加权PCA 比较 ===\n")

    # 全局PCA
    from sklearn.decomposition import PCA
    from sklearn.preprocessing import StandardScaler

    scaler = StandardScaler()
    data_scaled = scaler.fit_transform(data)

    global_pca = PCA(n_components=3)
    global_scores = global_pca.fit_transform(data_scaled)

    print("1. 全局PCA结果:")
    print(f"   解释方差比例: {global_pca.explained_variance_ratio_}")
    print(f"   累积解释方差比例: {np.cumsum(global_pca.explained_variance_ratio_)}")
    print("\n   全局主成分载荷:")

    # 保存全局PCA结果
    global_loadings_data = []
    for i, feature in enumerate(feature_names):
        print(f"     {feature}: PC1={global_pca.components_[0,i]:.4f}, "
              f"PC2={global_pca.components_[1,i]:.4f}, PC3={global_pca.components_[2,i]:.4f}")
        global_loadings_data.append({
            '特征': feature,
            'PC1载荷': global_pca.components_[0,i],
            'PC2载荷': global_pca.components_[1,i],
            'PC3载荷': global_pca.components_[2,i]
        })

    # 地理加权PCA
    gwpca = GWPCA(n_components=3, bandwidth='adaptive')
    local_scores = gwpca.fit_transform(data, coordinates)

    print(f"\n2. 地理加权PCA结果:")
    print(f"   选择的带宽: {gwpca.bandwidth_:.3f}")

    avg_summary = gwpca.get_local_summary()
    print(f"   平均解释方差比例: {avg_summary['平均解释方差比例'].values}")
    print(f"   累积解释方差比例: {avg_summary['累积解释方差比例'].values}")

    # 计算载荷的空间变异性
    variability = gwpca.get_spatial_variability()
    print(f"\n   载荷空间变异性 (第一主成分):")
    pc1_loading_var = variability['PC1']['平均载荷方差']
    print(f"     平均载荷方差: {pc1_loading_var:.6f}")

    # 保存比较结果
    comparison_data = {
        '方法': ['全局PCA', '地理加权PCA'],
        'PC1解释方差比例': [global_pca.explained_variance_ratio_[0], avg_summary['平均解释方差比例'].values[0]],
        'PC2解释方差比例': [global_pca.explained_variance_ratio_[1], avg_summary['平均解释方差比例'].values[1]],
        'PC3解释方差比例': [global_pca.explained_variance_ratio_[2], avg_summary['平均解释方差比例'].values[2]],
        '累积解释方差比例': [np.sum(global_pca.explained_variance_ratio_[:3]),
                      np.sum(avg_summary['平均解释方差比例'].values[:3])],
        '带宽': [None, gwpca.bandwidth_]
    }

    comparison_df = pd.DataFrame(comparison_data)
    comparison_df.to_csv(f"{OUTPUT_DIR}/results/pca_comparison.csv", index=False, encoding='utf-8-sig')

    # 保存全局PCA载荷
    global_loadings_df = pd.DataFrame(global_loadings_data)
    global_loadings_df.to_csv(f"{OUTPUT_DIR}/results/global_pca_loadings.csv", index=False, encoding='utf-8-sig')

    # 保存全局PCA得分
    coords_df = pd.DataFrame(coordinates, columns=['X坐标', 'Y坐标'])
    global_scores_df = pd.DataFrame(global_scores, columns=['全局PC1', '全局PC2', '全局PC3'])
    global_scores_with_coords = pd.concat([coords_df, global_scores_df], axis=1)
    global_scores_with_coords.to_csv(f"{OUTPUT_DIR}/data/global_pca_scores.csv", index=False, encoding='utf-8-sig')

    print(f"\n✅ 比较分析结果已保存:")
    print(f"   - PCA方法比较: {OUTPUT_DIR}/results/pca_comparison.csv")
    print(f"   - 全局PCA载荷: {OUTPUT_DIR}/results/global_pca_loadings.csv")
    print(f"   - 全局PCA得分: {OUTPUT_DIR}/data/global_pca_scores.csv")

    return global_pca, gwpca, global_scores, local_scores


def main_demo():
    """主演示函数"""
    print("🌍 地理加权主成分分析 (GWPCA) 完整演示\n")
    print("="*60)
    
    # 1. 生成数据
    print("\n📊 步骤1: 生成模拟地理空间数据")
    coordinates, data, feature_names = generate_realistic_spatial_data(n_points=150)
    print(f"   - 生成了 {len(coordinates)} 个样本点")
    print(f"   - 包含 {len(feature_names)} 个特征: {', '.join(feature_names)}")
    print(f"   - 研究区域: 10×10 单位")
    
    # 2. 可视化原始数据
    print("\n📈 步骤2: 可视化原始数据空间分布")
    visualize_spatial_data(coordinates, data, feature_names)
    
    # 3. 执行GWPCA
    print("\n🔍 步骤3: 执行地理加权主成分分析")
    gwpca = GWPCA(n_components=3, bandwidth='adaptive', kernel='gaussian')
    X_transformed = gwpca.fit_transform(data, coordinates)
    print(f"   - 自动选择的带宽: {gwpca.bandwidth_:.3f}")
    print(f"   - 转换后数据形状: {X_transformed.shape}")
    
    # 4. 可视化GWPCA结果
    print("\n📊 步骤4: 可视化GWPCA分析结果")
    visualize_gwpca_results(gwpca, coordinates, X_transformed, feature_names)
    
    # 5. 空间模式分析
    print("\n🔬 步骤5: 深入分析空间模式")
    analyze_spatial_patterns(gwpca, coordinates, feature_names)
    
    # 6. 比较分析
    print("\n⚖️ 步骤6: 全局PCA vs 地理加权PCA比较")
    global_pca, gwpca_final, global_scores, local_scores = compare_global_vs_local_pca(
        coordinates, data, feature_names)
    
    # 7. 总结
    print("\n" + "="*60)
    print("📋 分析总结:")
    print("1. GWPCA成功识别了数据的空间异质性")
    print("2. 不同地理位置的主成分结构存在显著差异")
    print("3. 局部分析提供了比全局PCA更丰富的空间信息")
    print("4. 空间权重函数有效捕捉了地理邻近效应")

    # 生成总结报告
    summary_report = {
        "分析日期": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S"),
        "数据概况": {
            "样本数量": len(coordinates),
            "特征数量": len(feature_names),
            "特征名称": feature_names,
            "研究区域": "10×10 单位"
        },
        "GWPCA参数": {
            "主成分数量": gwpca_final.n_components,
            "选择的带宽": gwpca_final.bandwidth_,
            "核函数": gwpca_final.kernel
        },
        "主要结果": {
            "平均解释方差比例": gwpca_final.get_local_summary()['平均解释方差比例'].tolist(),
            "累积解释方差比例": gwpca_final.get_local_summary()['累积解释方差比例'].tolist(),
            "空间变异性": gwpca_final.get_spatial_variability()
        },
        "文件输出": {
            "数据文件": [
                "original_data.csv",
                "pc_scores.csv",
                "local_explained_variance.csv",
                "pc1_loadings.csv",
                "global_pca_scores.csv"
            ],
            "结果文件": [
                "local_pca_summary.csv",
                "spatial_variability.csv",
                "specific_locations_analysis.csv",
                "pca_comparison.csv",
                "global_pca_loadings.csv"
            ],
            "图片文件": [
                "spatial_data_distribution.png",
                "pc_scores_spatial.png",
                "explained_variance_spatial.png",
                "pc1_loadings_spatial.png"
            ]
        }
    }

    # 保存总结报告
    with open(f"{OUTPUT_DIR}/results/analysis_summary.json", 'w', encoding='utf-8') as f:
        json.dump(summary_report, f, ensure_ascii=False, indent=2)

    # 创建README文件
    readme_content = f"""# 地理加权主成分分析 (GWPCA) 结果

## 分析概况
- 分析日期: {summary_report['分析日期']}
- 样本数量: {len(coordinates)}
- 特征数量: {len(feature_names)}
- 选择的带宽: {gwpca_final.bandwidth_:.3f}

## 文件结构

### data/ 目录
- `original_data.csv`: 原始数据（包含坐标和所有特征）
- `pc_scores.csv`: 主成分得分（包含坐标）
- `local_explained_variance.csv`: 局部解释方差比例（包含坐标）
- `pc1_loadings.csv`: 第一主成分载荷（包含坐标）
- `global_pca_scores.csv`: 全局PCA得分（包含坐标）

### results/ 目录
- `local_pca_summary.csv`: 局部主成分摘要统计
- `spatial_variability.csv`: 空间变异性分析结果
- `specific_locations_analysis.csv`: 特定位置详细分析
- `pca_comparison.csv`: 全局PCA vs 地理加权PCA比较
- `global_pca_loadings.csv`: 全局PCA载荷矩阵
- `analysis_summary.json`: 完整分析总结（JSON格式）

### figures/ 目录
- `spatial_data_distribution.png`: 原始数据空间分布图
- `pc_scores_spatial.png`: 主成分得分空间分布图
- `explained_variance_spatial.png`: 解释方差比例空间分布图
- `pc1_loadings_spatial.png`: 第一主成分载荷空间分布图

## 主要发现
1. GWPCA成功识别了数据的空间异质性
2. 不同地理位置的主成分结构存在显著差异
3. 局部分析提供了比全局PCA更丰富的空间信息
4. 空间权重函数有效捕捉了地理邻近效应

## 使用建议
- 可以在GIS软件中加载CSV文件进行进一步的空间分析
- 图片文件可用于报告和演示
- JSON文件包含了完整的分析参数和结果，便于重现分析
"""

    with open(f"{OUTPUT_DIR}/README.md", 'w', encoding='utf-8') as f:
        f.write(readme_content)

    print(f"\n✅ 完整分析结果已导出到: {OUTPUT_DIR}/")
    print(f"   - 数据文件: {len(summary_report['文件输出']['数据文件'])} 个")
    print(f"   - 结果文件: {len(summary_report['文件输出']['结果文件'])} 个")
    print(f"   - 图片文件: {len(summary_report['文件输出']['图片文件'])} 个")
    print(f"   - 总结报告: analysis_summary.json")
    print(f"   - 说明文档: README.md")

    return gwpca_final, coordinates, data, feature_names


if __name__ == "__main__":
    # 运行完整演示
    gwpca, coords, data, names = main_demo()
    
    print("\n" + "="*60)
    print("🎯 演示完成！")
    print("\n💡 提示：")
    print("- 可以尝试不同的带宽值和核函数")
    print("- 可以分析更多的主成分")
    print("- 可以在新位置进行预测")
    print("- 可以结合GIS软件进行更深入的空间分析")
