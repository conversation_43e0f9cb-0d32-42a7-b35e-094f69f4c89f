"""
地理加权主成分分析 (GWPCA) 简化演示文件

这个文件展示了GWPCA的核心功能，不包含图形界面
"""

import numpy as np
import pandas as pd
from gwpca import GWPCA
import warnings

warnings.filterwarnings('ignore')


def generate_realistic_spatial_data(n_points=100, region_size=10):
    """
    生成更真实的地理空间数据
    """
    np.random.seed(42)
    
    # 生成随机分布的坐标点
    coordinates = np.random.uniform(0, region_size, (n_points, 2))
    
    # 特征名称
    feature_names = ['人口密度', '经济发展水平', '教育水平', '环境质量', '交通便利性']
    n_features = len(feature_names)
    
    data = np.zeros((n_points, n_features))
    
    for i, (x, y) in enumerate(coordinates):
        # 人口密度：城市中心（5,5）附近密度高
        center_dist = np.sqrt((x - region_size/2)**2 + (y - region_size/2)**2)
        data[i, 0] = 100 * np.exp(-center_dist/3) + np.random.normal(0, 10)
        
        # 经济发展水平：与人口密度正相关，但有东西梯度
        data[i, 1] = 0.8 * data[i, 0] + 5 * x + np.random.normal(0, 15)
        
        # 教育水平：与经济发展相关，但有南北差异
        data[i, 2] = 0.6 * data[i, 1] + 3 * y + np.random.normal(0, 12)
        
        # 环境质量：远离中心区域质量更好
        data[i, 3] = 80 - 0.3 * data[i, 0] + np.random.normal(0, 8)
        
        # 交通便利性：主要道路（对角线）附近更便利
        road_dist = abs(x - y) + abs(x + y - region_size)
        data[i, 4] = 60 - 2 * road_dist + 0.2 * data[i, 0] + np.random.normal(0, 10)
    
    # 确保数据为正值
    data = np.maximum(data, 0)
    
    return coordinates, data, feature_names


def analyze_spatial_patterns(gwpca, coordinates, feature_names):
    """分析空间模式"""
    print("=== 空间模式分析 ===\n")
    
    # 1. 整体摘要
    print("1. 局部主成分摘要（所有位置平均）:")
    summary = gwpca.get_local_summary()
    print(summary.round(4))
    print()
    
    # 2. 空间变异性
    print("2. 主成分的空间变异性:")
    variability = gwpca.get_spatial_variability()
    for pc_name, metrics in variability.items():
        print(f"   {pc_name}:")
        for metric_name, value in metrics.items():
            print(f"     - {metric_name}: {value:.6f}")
    print()
    
    # 3. 特定位置的详细分析
    print("3. 特定位置的主成分分析:")
    
    # 选择几个代表性位置
    center_idx = np.argmin(np.sum((coordinates - np.array([5, 5]))**2, axis=1))
    corner_idx = np.argmin(np.sum((coordinates - np.array([0, 0]))**2, axis=1))
    edge_idx = np.argmin(np.sum((coordinates - np.array([10, 5]))**2, axis=1))
    
    locations = [
        (center_idx, "中心区域"),
        (corner_idx, "边角区域"), 
        (edge_idx, "边缘区域")
    ]
    
    for idx, location_name in locations:
        print(f"\n   {location_name} (坐标: {coordinates[idx].round(2)}):")
        local_summary = gwpca.get_local_summary(idx)
        print(f"     第一主成分解释方差比例: {local_summary.iloc[0]['解释方差比例']:.4f}")
        
        # 显示第一主成分的载荷
        loadings = gwpca.local_components_[idx, 0, :]
        print("     第一主成分载荷:")
        for i, (feature, loading) in enumerate(zip(feature_names, loadings)):
            print(f"       {feature}: {loading:.4f}")


def compare_global_vs_local_pca(coordinates, data, feature_names):
    """比较全局PCA和地理加权PCA"""
    print("=== 全局PCA vs 地理加权PCA 比较 ===\n")
    
    # 全局PCA
    from sklearn.decomposition import PCA
    from sklearn.preprocessing import StandardScaler
    
    scaler = StandardScaler()
    data_scaled = scaler.fit_transform(data)
    
    global_pca = PCA(n_components=3)
    global_scores = global_pca.fit_transform(data_scaled)
    
    print("1. 全局PCA结果:")
    print(f"   解释方差比例: {global_pca.explained_variance_ratio_.round(4)}")
    print(f"   累积解释方差比例: {np.cumsum(global_pca.explained_variance_ratio_).round(4)}")
    print("\n   全局主成分载荷:")
    for i, feature in enumerate(feature_names):
        print(f"     {feature}: PC1={global_pca.components_[0,i]:.4f}, "
              f"PC2={global_pca.components_[1,i]:.4f}, PC3={global_pca.components_[2,i]:.4f}")
    
    # 地理加权PCA
    gwpca = GWPCA(n_components=3, bandwidth='adaptive')
    local_scores = gwpca.fit_transform(data, coordinates)
    
    print(f"\n2. 地理加权PCA结果:")
    print(f"   选择的带宽: {gwpca.bandwidth_:.3f}")
    
    avg_summary = gwpca.get_local_summary()
    print(f"   平均解释方差比例: {avg_summary['平均解释方差比例'].values.round(4)}")
    print(f"   累积解释方差比例: {avg_summary['累积解释方差比例'].values.round(4)}")
    
    # 计算载荷的空间变异性
    variability = gwpca.get_spatial_variability()
    print(f"\n   载荷空间变异性 (第一主成分):")
    pc1_loading_var = variability['PC1']['平均载荷方差']
    print(f"     平均载荷方差: {pc1_loading_var:.6f}")
    
    # 比较解释方差
    global_total_var = np.sum(global_pca.explained_variance_ratio_[:3])
    local_total_var = np.sum(avg_summary['平均解释方差比例'].values[:3])
    
    print(f"\n3. 解释方差比较:")
    print(f"   全局PCA前3个主成分累积解释方差: {global_total_var:.4f}")
    print(f"   地理加权PCA前3个主成分平均累积解释方差: {local_total_var:.4f}")
    
    return global_pca, gwpca, global_scores, local_scores


def demonstrate_prediction():
    """演示预测功能"""
    print("\n=== 预测功能演示 ===\n")
    
    # 生成训练数据
    coordinates, data, feature_names = generate_realistic_spatial_data(n_points=80)
    
    # 训练GWPCA模型
    gwpca = GWPCA(n_components=3, bandwidth='adaptive')
    gwpca.fit(data, coordinates)
    
    print(f"1. 训练数据:")
    print(f"   - 训练样本数: {len(coordinates)}")
    print(f"   - 选择的带宽: {gwpca.bandwidth_:.3f}")
    
    # 生成新的预测位置
    new_coordinates = np.array([
        [2.5, 7.5],  # 左上角
        [8.0, 3.0],  # 右下角
        [5.0, 5.0],  # 中心
        [1.0, 1.0]   # 左下角
    ])
    
    # 为新位置生成数据（模拟真实场景中的新观测）
    new_data = np.zeros((len(new_coordinates), len(feature_names)))
    for i, (x, y) in enumerate(new_coordinates):
        center_dist = np.sqrt((x - 5)**2 + (y - 5)**2)
        new_data[i, 0] = 100 * np.exp(-center_dist/3) + np.random.normal(0, 5)
        new_data[i, 1] = 0.8 * new_data[i, 0] + 5 * x + np.random.normal(0, 8)
        new_data[i, 2] = 0.6 * new_data[i, 1] + 3 * y + np.random.normal(0, 6)
        new_data[i, 3] = 80 - 0.3 * new_data[i, 0] + np.random.normal(0, 4)
        road_dist = abs(x - y) + abs(x + y - 10)
        new_data[i, 4] = 60 - 2 * road_dist + 0.2 * new_data[i, 0] + np.random.normal(0, 5)
    
    new_data = np.maximum(new_data, 0)
    
    # 进行预测
    predictions = gwpca.predict_at_location(new_coordinates, new_data)
    
    print(f"\n2. 预测结果:")
    location_names = ['左上角', '右下角', '中心', '左下角']
    
    for i, (coord, name, pred) in enumerate(zip(new_coordinates, location_names, predictions)):
        print(f"   {name} (坐标: {coord}):")
        print(f"     主成分得分: PC1={pred[0]:.3f}, PC2={pred[1]:.3f}, PC3={pred[2]:.3f}")
        
        # 显示该位置的局部主成分特性（通过插值）
        interpolated_components = gwpca._interpolate_components(coord)
        print(f"     第一主成分载荷 (插值):")
        for j, feature in enumerate(feature_names):
            print(f"       {feature}: {interpolated_components[0, j]:.4f}")
        print()


def main_demo():
    """主演示函数"""
    print("🌍 地理加权主成分分析 (GWPCA) 完整演示\n")
    print("="*60)
    
    # 1. 生成数据
    print("\n📊 步骤1: 生成模拟地理空间数据")
    coordinates, data, feature_names = generate_realistic_spatial_data(n_points=150)
    print(f"   - 生成了 {len(coordinates)} 个样本点")
    print(f"   - 包含 {len(feature_names)} 个特征: {', '.join(feature_names)}")
    print(f"   - 研究区域: 10×10 单位")
    
    # 2. 数据统计
    print(f"\n📈 步骤2: 数据基本统计")
    print(f"   特征统计摘要:")
    data_df = pd.DataFrame(data, columns=feature_names)
    print(data_df.describe().round(2))
    
    # 3. 执行GWPCA
    print(f"\n🔍 步骤3: 执行地理加权主成分分析")
    gwpca = GWPCA(n_components=3, bandwidth='adaptive', kernel='gaussian')
    X_transformed = gwpca.fit_transform(data, coordinates)
    print(f"   - 自动选择的带宽: {gwpca.bandwidth_:.3f}")
    print(f"   - 转换后数据形状: {X_transformed.shape}")
    
    # 4. 空间模式分析
    print(f"\n🔬 步骤4: 深入分析空间模式")
    analyze_spatial_patterns(gwpca, coordinates, feature_names)
    
    # 5. 比较分析
    print(f"\n⚖️ 步骤5: 全局PCA vs 地理加权PCA比较")
    global_pca, gwpca_final, global_scores, local_scores = compare_global_vs_local_pca(
        coordinates, data, feature_names)
    
    # 6. 预测演示
    demonstrate_prediction()
    
    # 7. 总结
    print("="*60)
    print("📋 分析总结:")
    print("1. ✅ GWPCA成功识别了数据的空间异质性")
    print("2. ✅ 不同地理位置的主成分结构存在显著差异")
    print("3. ✅ 局部分析提供了比全局PCA更丰富的空间信息")
    print("4. ✅ 空间权重函数有效捕捉了地理邻近效应")
    print("5. ✅ 预测功能可以为新位置提供局部主成分分析")
    
    return gwpca_final, coordinates, data, feature_names


if __name__ == "__main__":
    # 运行完整演示
    gwpca, coords, data, names = main_demo()
    
    print("\n" + "="*60)
    print("🎯 演示完成！")
    print("\n💡 使用建议：")
    print("- 可以尝试不同的带宽值和核函数")
    print("- 可以分析更多的主成分")
    print("- 可以在新位置进行预测")
    print("- 可以结合GIS软件进行更深入的空间分析")
    print("- 适用于环境科学、城市规划、经济地理等领域")
