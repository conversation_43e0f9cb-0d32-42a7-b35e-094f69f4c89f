# 地理加权主成分分析 (GWPCA) 结果

## 分析概况
- 分析日期: 2025-07-29 16:46:57
- 样本数量: 150
- 特征数量: 5
- 选择的带宽: 1.343

## 文件结构

### data/ 目录
- `original_data.csv`: 原始数据（包含坐标和所有特征）
- `pc_scores.csv`: 主成分得分（包含坐标）
- `local_explained_variance.csv`: 局部解释方差比例（包含坐标）
- `pc1_loadings.csv`: 第一主成分载荷（包含坐标）
- `global_pca_scores.csv`: 全局PCA得分（包含坐标）

### results/ 目录
- `local_pca_summary.csv`: 局部主成分摘要统计
- `spatial_variability.csv`: 空间变异性分析结果
- `specific_locations_analysis.csv`: 特定位置详细分析
- `pca_comparison.csv`: 全局PCA vs 地理加权PCA比较
- `global_pca_loadings.csv`: 全局PCA载荷矩阵
- `analysis_summary.json`: 完整分析总结（JSON格式）

### figures/ 目录
- `spatial_data_distribution.png`: 原始数据空间分布图
- `pc_scores_spatial.png`: 主成分得分空间分布图
- `explained_variance_spatial.png`: 解释方差比例空间分布图
- `pc1_loadings_spatial.png`: 第一主成分载荷空间分布图

## 主要发现
1. GWPCA成功识别了数据的空间异质性
2. 不同地理位置的主成分结构存在显著差异
3. 局部分析提供了比全局PCA更丰富的空间信息
4. 空间权重函数有效捕捉了地理邻近效应

## 使用建议
- 可以在GIS软件中加载CSV文件进行进一步的空间分析
- 图片文件可用于报告和演示
- JSON文件包含了完整的分析参数和结果，便于重现分析
