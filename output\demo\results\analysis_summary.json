{"分析日期": "2025-07-29 16:36:22", "数据概况": {"样本数量": 150, "特征数量": 5, "特征名称": ["人口密度", "经济发展水平", "教育水平", "环境质量", "交通便利性"], "研究区域": "10×10 单位"}, "GWPCA参数": {"主成分数量": 3, "选择的带宽": 1.3425077132747048, "核函数": "gaussian"}, "主要结果": {"平均解释方差比例": [0.5856383713545906, 0.19964833989095282, 0.12399308338994987], "累积解释方差比例": [0.5856383713545906, 0.7852867112455435, 0.9092797946354934], "空间变异性": {"PC1": {"特征值方差": 1.8036169876587613, "解释方差比例方差": 0.007562105830799912, "平均载荷方差": 0.09579836959093044}, "PC2": {"特征值方差": 0.03741367358107689, "解释方差比例方差": 0.0039041188049728603, "平均载荷方差": 0.17800260795503142}, "PC3": {"特征值方差": 0.007995820820785633, "解释方差比例方差": 0.0006510906824093838, "平均载荷方差": 0.1826820140872584}}}, "文件输出": {"数据文件": ["original_data.csv", "pc_scores.csv", "local_explained_variance.csv", "pc1_loadings.csv", "global_pca_scores.csv"], "结果文件": ["local_pca_summary.csv", "spatial_variability.csv", "specific_locations_analysis.csv", "pca_comparison.csv", "global_pca_loadings.csv"], "图片文件": ["spatial_data_distribution.png", "pc_scores_spatial.png", "explained_variance_spatial.png", "pc1_loadings_spatial.png"]}}