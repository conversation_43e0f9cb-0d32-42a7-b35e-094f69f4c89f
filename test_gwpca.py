"""
地理加权主成分分析 (GWPCA) 测试文件

这个文件包含了对GWPCA实现的全面测试，包括：
- 基本功能测试
- 空间权重计算测试
- 局部PCA分析测试
- 边界条件测试
- 性能测试
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import pytest
from gwpca import GWPCA
import warnings

# 忽略一些不重要的警告
warnings.filterwarnings('ignore')


class TestGWPCA:
    """GWPCA测试类"""
    
    def setup_method(self):
        """设置测试数据"""
        # 创建模拟的地理数据
        np.random.seed(42)
        
        # 生成网格坐标
        x = np.linspace(0, 10, 20)
        y = np.linspace(0, 10, 20)
        xx, yy = np.meshgrid(x, y)
        self.coordinates = np.column_stack([xx.ravel(), yy.ravel()])
        
        n_samples = len(self.coordinates)
        n_features = 5
        
        # 生成具有空间相关性的数据
        self.X = self._generate_spatial_data(self.coordinates, n_features)
        
        # 创建GWPCA实例
        self.gwpca = GWPCA(n_components=3, bandwidth=2.0, kernel='gaussian')
    
    def _generate_spatial_data(self, coordinates, n_features):
        """生成具有空间相关性的测试数据"""
        n_samples = coordinates.shape[0]
        X = np.zeros((n_samples, n_features))
        
        for i in range(n_samples):
            x, y = coordinates[i]
            
            # 特征1：基于x坐标的线性趋势
            X[i, 0] = 2 * x + np.random.normal(0, 0.5)
            
            # 特征2：基于y坐标的线性趋势
            X[i, 1] = 1.5 * y + np.random.normal(0, 0.5)
            
            # 特征3：基于距离中心的径向模式
            center_dist = np.sqrt((x - 5)**2 + (y - 5)**2)
            X[i, 2] = 10 - center_dist + np.random.normal(0, 0.3)
            
            # 特征4：交互效应
            X[i, 3] = x * y * 0.1 + np.random.normal(0, 0.4)
            
            # 特征5：随机噪声
            X[i, 4] = np.random.normal(0, 1)
            
        return X
    
    def test_initialization(self):
        """测试GWPCA初始化"""
        # 测试默认参数
        gwpca_default = GWPCA()
        assert gwpca_default.n_components is None
        assert gwpca_default.bandwidth == 'adaptive'
        assert gwpca_default.kernel == 'gaussian'
        assert gwpca_default.standardize == True
        assert gwpca_default.center == True
        
        # 测试自定义参数
        gwpca_custom = GWPCA(n_components=2, bandwidth=1.5, kernel='bisquare', 
                           standardize=False, center=False)
        assert gwpca_custom.n_components == 2
        assert gwpca_custom.bandwidth == 1.5
        assert gwpca_custom.kernel == 'bisquare'
        assert gwpca_custom.standardize == False
        assert gwpca_custom.center == False
    
    def test_fit(self):
        """测试模型拟合"""
        # 基本拟合测试
        self.gwpca.fit(self.X, self.coordinates)
        
        # 检查拟合后的属性
        assert self.gwpca.coordinates_ is not None
        assert self.gwpca.local_components_ is not None
        assert self.gwpca.local_eigenvalues_ is not None
        assert self.gwpca.local_explained_variance_ratio_ is not None
        assert self.gwpca.bandwidth_ is not None
        
        # 检查形状
        n_samples, n_features = self.X.shape
        assert self.gwpca.local_components_.shape == (n_samples, 3, n_features)
        assert self.gwpca.local_eigenvalues_.shape == (n_samples, 3)
        assert self.gwpca.local_explained_variance_ratio_.shape == (n_samples, 3)
    
    def test_transform(self):
        """测试数据转换"""
        # 先拟合模型
        self.gwpca.fit(self.X, self.coordinates)
        
        # 转换数据
        X_transformed = self.gwpca.transform(self.X)
        
        # 检查转换结果
        assert X_transformed.shape == (len(self.X), 3)
        assert not np.any(np.isnan(X_transformed))
        assert not np.any(np.isinf(X_transformed))
    
    def test_fit_transform(self):
        """测试拟合并转换"""
        X_transformed = self.gwpca.fit_transform(self.X, self.coordinates)
        
        # 检查结果
        assert X_transformed.shape == (len(self.X), 3)
        assert not np.any(np.isnan(X_transformed))
        
        # 与分步操作比较
        gwpca2 = GWPCA(n_components=3, bandwidth=2.0, kernel='gaussian')
        gwpca2.fit(self.X, self.coordinates)
        X_transformed2 = gwpca2.transform(self.X)
        
        np.testing.assert_array_almost_equal(X_transformed, X_transformed2)
    
    def test_spatial_weights(self):
        """测试空间权重计算"""
        self.gwpca.bandwidth_ = 2.0
        
        # 测试高斯核
        self.gwpca.kernel = 'gaussian'
        target_coord = np.array([5.0, 5.0])
        weights = self.gwpca._compute_spatial_weights(target_coord, self.coordinates)
        
        # 检查权重属性
        assert len(weights) == len(self.coordinates)
        assert np.all(weights >= 0)
        assert np.all(weights <= 1)
        
        # 检查距离越近权重越大
        distances = np.sqrt(np.sum((self.coordinates - target_coord)**2, axis=1))
        closest_idx = np.argmin(distances)
        assert weights[closest_idx] == np.max(weights)
        
        # 测试其他核函数
        for kernel in ['bisquare', 'exponential', 'tricube']:
            self.gwpca.kernel = kernel
            weights = self.gwpca._compute_spatial_weights(target_coord, self.coordinates)
            assert len(weights) == len(self.coordinates)
            assert np.all(weights >= 0)
    
    def test_bandwidth_selection(self):
        """测试自适应带宽选择"""
        gwpca_adaptive = GWPCA(bandwidth='adaptive')
        gwpca_adaptive.fit(self.X, self.coordinates)
        
        # 检查带宽是否被正确设置
        assert gwpca_adaptive.bandwidth_ is not None
        assert gwpca_adaptive.bandwidth_ > 0
        assert isinstance(gwpca_adaptive.bandwidth_, float)
    
    def test_local_covariance(self):
        """测试局部协方差矩阵计算"""
        # 预处理数据
        X_processed = self.gwpca._preprocess_data(self.X)
        
        # 创建均匀权重
        weights = np.ones(len(self.X)) / len(self.X)
        
        # 计算局部协方差矩阵
        cov_matrix = self.gwpca._compute_local_covariance(X_processed, weights)
        
        # 检查协方差矩阵属性
        n_features = self.X.shape[1]
        assert cov_matrix.shape == (n_features, n_features)
        assert np.allclose(cov_matrix, cov_matrix.T)  # 对称性
        
        # 检查正定性（特征值都应该非负）
        eigenvalues = np.linalg.eigvals(cov_matrix)
        assert np.all(eigenvalues >= -1e-10)  # 允许小的数值误差
    
    def test_interpolation(self):
        """测试成分插值"""
        # 先拟合模型
        self.gwpca.fit(self.X, self.coordinates)
        
        # 测试插值
        new_coord = np.array([2.5, 7.5])
        interpolated_components = self.gwpca._interpolate_components(new_coord)
        
        # 检查插值结果
        assert interpolated_components.shape == (3, self.X.shape[1])
        assert not np.any(np.isnan(interpolated_components))
    
    def test_prediction_at_new_location(self):
        """测试在新位置的预测"""
        # 先拟合模型
        self.gwpca.fit(self.X, self.coordinates)
        
        # 创建新位置和数据
        new_coordinates = np.array([[2.5, 7.5], [8.0, 3.0]])
        new_X = self._generate_spatial_data(new_coordinates, self.X.shape[1])
        
        # 预测
        predictions = self.gwpca.predict_at_location(new_coordinates, new_X)
        
        # 检查预测结果
        assert predictions.shape == (2, 3)
        assert not np.any(np.isnan(predictions))
    
    def test_summary_methods(self):
        """测试摘要方法"""
        # 先拟合模型
        self.gwpca.fit(self.X, self.coordinates)
        
        # 测试局部摘要
        summary_all = self.gwpca.get_local_summary()
        assert isinstance(summary_all, pd.DataFrame)
        assert len(summary_all) == 3  # 3个主成分
        
        # 测试特定位置摘要
        summary_specific = self.gwpca.get_local_summary(location_index=0)
        assert isinstance(summary_specific, pd.DataFrame)
        assert len(summary_specific) == 3
        
        # 测试空间变异性
        variability = self.gwpca.get_spatial_variability()
        assert isinstance(variability, dict)
        assert len(variability) == 3  # 3个主成分
        for pc_name, metrics in variability.items():
            assert '特征值方差' in metrics
            assert '解释方差比例方差' in metrics
            assert '平均载荷方差' in metrics
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试未拟合模型的错误
        gwpca_unfitted = GWPCA()
        
        with pytest.raises(ValueError, match="模型尚未拟合"):
            gwpca_unfitted.transform(self.X)
        
        with pytest.raises(ValueError, match="模型尚未拟合"):
            gwpca_unfitted.get_local_summary()
        
        # 测试坐标维度错误
        wrong_coordinates = np.random.rand(len(self.X), 3)  # 3维坐标
        with pytest.raises(ValueError, match="坐标必须是二维的"):
            self.gwpca.fit(self.X, wrong_coordinates)
        
        # 测试坐标数量不匹配
        wrong_coordinates = np.random.rand(len(self.X) - 1, 2)
        with pytest.raises(ValueError, match="坐标数量必须与样本数量相等"):
            self.gwpca.fit(self.X, wrong_coordinates)
        
        # 测试不支持的核函数
        gwpca_wrong_kernel = GWPCA(kernel='unknown')
        gwpca_wrong_kernel.bandwidth_ = 1.0
        with pytest.raises(ValueError, match="不支持的核函数"):
            gwpca_wrong_kernel._compute_spatial_weights(np.array([0, 0]), self.coordinates)


def run_basic_example():
    """运行基本使用示例"""
    print("=== 地理加权主成分分析 (GWPCA) 基本示例 ===\n")
    
    # 创建测试数据
    np.random.seed(42)
    test_gwpca = TestGWPCA()
    test_gwpca.setup_method()
    
    print("1. 创建GWPCA模型并拟合数据")
    gwpca = GWPCA(n_components=3, bandwidth='adaptive', kernel='gaussian')
    gwpca.fit(test_gwpca.X, test_gwpca.coordinates)
    print(f"   - 样本数量: {len(test_gwpca.X)}")
    print(f"   - 特征数量: {test_gwpca.X.shape[1]}")
    print(f"   - 选择的带宽: {gwpca.bandwidth_:.3f}")
    
    print("\n2. 数据转换")
    X_transformed = gwpca.transform(test_gwpca.X)
    print(f"   - 转换后数据形状: {X_transformed.shape}")
    
    print("\n3. 局部主成分摘要（平均）")
    summary = gwpca.get_local_summary()
    print(summary.round(4))
    
    print("\n4. 空间变异性分析")
    variability = gwpca.get_spatial_variability()
    for pc_name, metrics in variability.items():
        print(f"   {pc_name}:")
        for metric_name, value in metrics.items():
            print(f"     - {metric_name}: {value:.6f}")
    
    print("\n5. 在新位置进行预测")
    new_coords = np.array([[2.5, 7.5], [8.0, 3.0]])
    new_X = test_gwpca._generate_spatial_data(new_coords, test_gwpca.X.shape[1])
    predictions = gwpca.predict_at_location(new_coords, new_X)
    print(f"   - 新位置坐标: {new_coords}")
    print(f"   - 预测结果形状: {predictions.shape}")
    print(f"   - 预测值:\n{predictions.round(4)}")


if __name__ == "__main__":
    # 运行基本示例
    run_basic_example()
    
    print("\n" + "="*60)
    print("运行完整测试套件...")
    
    # 运行测试
    test_instance = TestGWPCA()
    test_methods = [method for method in dir(test_instance) if method.startswith('test_')]
    
    passed_tests = 0
    total_tests = len(test_methods)
    
    for test_method in test_methods:
        try:
            test_instance.setup_method()
            getattr(test_instance, test_method)()
            print(f"✓ {test_method}")
            passed_tests += 1
        except Exception as e:
            print(f"✗ {test_method}: {str(e)}")
    
    print(f"\n测试结果: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！GWPCA实现正确。")
    else:
        print("⚠️  部分测试失败，请检查实现。")
