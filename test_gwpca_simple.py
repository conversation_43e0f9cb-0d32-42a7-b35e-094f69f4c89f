"""
地理加权主成分分析 (GWPCA) 简化测试文件

这个文件包含了对GWPCA实现的基本测试，不依赖matplotlib和seaborn
"""

import numpy as np
import pandas as pd
from gwpca import GWPCA
import warnings

# 忽略一些不重要的警告
warnings.filterwarnings('ignore')


def generate_test_data():
    """生成测试数据"""
    np.random.seed(42)
    
    # 生成网格坐标
    x = np.linspace(0, 10, 20)
    y = np.linspace(0, 10, 20)
    xx, yy = np.meshgrid(x, y)
    coordinates = np.column_stack([xx.ravel(), yy.ravel()])
    
    n_samples = len(coordinates)
    n_features = 5
    
    # 生成具有空间相关性的数据
    X = np.zeros((n_samples, n_features))
    
    for i in range(n_samples):
        x_coord, y_coord = coordinates[i]
        
        # 特征1：基于x坐标的线性趋势
        X[i, 0] = 2 * x_coord + np.random.normal(0, 0.5)
        
        # 特征2：基于y坐标的线性趋势
        X[i, 1] = 1.5 * y_coord + np.random.normal(0, 0.5)
        
        # 特征3：基于距离中心的径向模式
        center_dist = np.sqrt((x_coord - 5)**2 + (y_coord - 5)**2)
        X[i, 2] = 10 - center_dist + np.random.normal(0, 0.3)
        
        # 特征4：交互效应
        X[i, 3] = x_coord * y_coord * 0.1 + np.random.normal(0, 0.4)
        
        # 特征5：随机噪声
        X[i, 4] = np.random.normal(0, 1)
    
    return coordinates, X


def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试基本功能 ===")
    
    coordinates, X = generate_test_data()
    
    # 创建GWPCA实例
    gwpca = GWPCA(n_components=3, bandwidth=2.0, kernel='gaussian')
    
    print(f"✓ 创建GWPCA实例成功")
    print(f"  - 数据形状: {X.shape}")
    print(f"  - 坐标形状: {coordinates.shape}")
    
    # 拟合模型
    gwpca.fit(X, coordinates)
    print(f"✓ 模型拟合成功")
    print(f"  - 使用带宽: {gwpca.bandwidth_}")
    print(f"  - 局部成分形状: {gwpca.local_components_.shape}")
    
    # 转换数据
    X_transformed = gwpca.transform(X)
    print(f"✓ 数据转换成功")
    print(f"  - 转换后形状: {X_transformed.shape}")
    
    # 检查结果
    assert not np.any(np.isnan(X_transformed)), "转换结果包含NaN值"
    assert not np.any(np.isinf(X_transformed)), "转换结果包含无穷值"
    print(f"✓ 结果验证通过")
    
    return gwpca, coordinates, X, X_transformed


def test_spatial_weights():
    """测试空间权重计算"""
    print("\n=== 测试空间权重计算 ===")
    
    coordinates, _ = generate_test_data()
    gwpca = GWPCA(bandwidth=2.0, kernel='gaussian')
    gwpca.bandwidth_ = 2.0
    
    # 测试不同核函数
    kernels = ['gaussian', 'bisquare', 'exponential', 'tricube']
    target_coord = np.array([5.0, 5.0])
    
    for kernel in kernels:
        gwpca.kernel = kernel
        weights = gwpca._compute_spatial_weights(target_coord, coordinates)
        
        # 检查权重属性
        assert len(weights) == len(coordinates), f"{kernel}核权重长度不正确"
        assert np.all(weights >= 0), f"{kernel}核权重包含负值"
        
        # 检查距离越近权重越大
        distances = np.sqrt(np.sum((coordinates - target_coord)**2, axis=1))
        closest_idx = np.argmin(distances)
        assert weights[closest_idx] == np.max(weights), f"{kernel}核权重距离关系不正确"
        
        print(f"✓ {kernel}核函数测试通过")


def test_adaptive_bandwidth():
    """测试自适应带宽选择"""
    print("\n=== 测试自适应带宽选择 ===")
    
    coordinates, X = generate_test_data()
    gwpca = GWPCA(bandwidth='adaptive')
    
    gwpca.fit(X, coordinates)
    
    assert gwpca.bandwidth_ is not None, "自适应带宽未设置"
    assert gwpca.bandwidth_ > 0, "自适应带宽为负值或零"
    assert isinstance(gwpca.bandwidth_, float), "自适应带宽类型不正确"
    
    print(f"✓ 自适应带宽选择成功: {gwpca.bandwidth_:.3f}")


def test_local_analysis():
    """测试局部分析功能"""
    print("\n=== 测试局部分析功能 ===")
    
    coordinates, X = generate_test_data()
    gwpca = GWPCA(n_components=3, bandwidth=2.0)
    gwpca.fit(X, coordinates)
    
    # 测试局部摘要
    summary_all = gwpca.get_local_summary()
    assert isinstance(summary_all, pd.DataFrame), "局部摘要类型不正确"
    assert len(summary_all) == 3, "局部摘要长度不正确"
    print(f"✓ 全局局部摘要测试通过")
    
    # 测试特定位置摘要
    summary_specific = gwpca.get_local_summary(location_index=0)
    assert isinstance(summary_specific, pd.DataFrame), "特定位置摘要类型不正确"
    assert len(summary_specific) == 3, "特定位置摘要长度不正确"
    print(f"✓ 特定位置摘要测试通过")
    
    # 测试空间变异性
    variability = gwpca.get_spatial_variability()
    assert isinstance(variability, dict), "空间变异性类型不正确"
    assert len(variability) == 3, "空间变异性长度不正确"
    
    for pc_name, metrics in variability.items():
        assert '特征值方差' in metrics, f"{pc_name}缺少特征值方差"
        assert '解释方差比例方差' in metrics, f"{pc_name}缺少解释方差比例方差"
        assert '平均载荷方差' in metrics, f"{pc_name}缺少平均载荷方差"
    
    print(f"✓ 空间变异性分析测试通过")


def test_prediction():
    """测试预测功能"""
    print("\n=== 测试预测功能 ===")
    
    coordinates, X = generate_test_data()
    gwpca = GWPCA(n_components=3, bandwidth=2.0)
    gwpca.fit(X, coordinates)
    
    # 创建新位置和数据
    new_coordinates = np.array([[2.5, 7.5], [8.0, 3.0]])
    new_X = np.random.randn(2, X.shape[1])
    
    # 预测
    predictions = gwpca.predict_at_location(new_coordinates, new_X)
    
    assert predictions.shape == (2, 3), "预测结果形状不正确"
    assert not np.any(np.isnan(predictions)), "预测结果包含NaN值"
    assert not np.any(np.isinf(predictions)), "预测结果包含无穷值"
    
    print(f"✓ 新位置预测测试通过")
    print(f"  - 预测形状: {predictions.shape}")


def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    coordinates, X = generate_test_data()
    
    # 测试未拟合模型的错误
    gwpca_unfitted = GWPCA()
    
    try:
        gwpca_unfitted.transform(X)
        assert False, "应该抛出未拟合错误"
    except ValueError as e:
        assert "模型尚未拟合" in str(e)
        print("✓ 未拟合模型错误处理正确")
    
    # 测试坐标维度错误
    wrong_coordinates = np.random.rand(len(X), 3)  # 3维坐标
    gwpca = GWPCA()
    
    try:
        gwpca.fit(X, wrong_coordinates)
        assert False, "应该抛出坐标维度错误"
    except ValueError as e:
        assert "坐标必须是二维的" in str(e)
        print("✓ 坐标维度错误处理正确")
    
    # 测试坐标数量不匹配
    wrong_coordinates = np.random.rand(len(X) - 1, 2)
    
    try:
        gwpca.fit(X, wrong_coordinates)
        assert False, "应该抛出坐标数量错误"
    except ValueError as e:
        assert "坐标数量必须与样本数量相等" in str(e)
        print("✓ 坐标数量错误处理正确")


def run_comprehensive_test():
    """运行综合测试"""
    print("🌍 地理加权主成分分析 (GWPCA) 综合测试")
    print("=" * 60)
    
    try:
        # 基本功能测试
        gwpca, coordinates, X, X_transformed = test_basic_functionality()
        
        # 空间权重测试
        test_spatial_weights()
        
        # 自适应带宽测试
        test_adaptive_bandwidth()
        
        # 局部分析测试
        test_local_analysis()
        
        # 预测功能测试
        test_prediction()
        
        # 错误处理测试
        test_error_handling()
        
        print("\n" + "=" * 60)
        print("🎉 所有测试通过！GWPCA实现正确。")
        
        # 显示一些基本统计信息
        print(f"\n📊 基本统计信息:")
        print(f"  - 样本数量: {len(X)}")
        print(f"  - 特征数量: {X.shape[1]}")
        print(f"  - 主成分数量: {X_transformed.shape[1]}")
        print(f"  - 使用带宽: {gwpca.bandwidth_:.3f}")
        
        # 显示局部摘要
        print(f"\n📈 局部主成分摘要:")
        summary = gwpca.get_local_summary()
        print(summary.round(4))
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_comprehensive_test()
    
    if success:
        print(f"\n💡 提示:")
        print(f"  - 可以运行 'python gwpca_demo.py' 查看完整演示")
        print(f"  - 可以尝试不同的参数设置")
        print(f"  - 可以应用到真实的地理空间数据")
    else:
        print(f"\n⚠️ 请检查实现并修复错误")
